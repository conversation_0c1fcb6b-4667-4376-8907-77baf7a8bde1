"""
Simple test for vision-enhanced extraction capabilities.
Tests organizational level extraction with vision support.
"""

import asyncio
import json
import os
import time
from datetime import datetime

from src.vision_enhanced_openai_client import VisionEnhancedOpenAIClient
from src.serp_client import SerpAPIClient, PowerPlantSearchOrchestrator
from src.scraper_client import ScraperAPIClient

async def test_vision_enhanced_extraction():
    """Test vision-enhanced extraction on a simple example."""
    
    print("🧪 TESTING VISION-ENHANCED EXTRACTION")
    print("=" * 50)
    
    # Get API keys
    openai_api_key = os.getenv("OPENAI_API_KEY")
    serp_api_key = os.getenv("SERP_API_KEY")
    scraper_api_key = os.getenv("SCRAPER_API_KEY")
    openai_model = os.getenv("OPENAI_MODEL", "gpt-4o-mini")
    
    if not all([openai_api_key, serp_api_key, scraper_api_key]):
        print("❌ Missing required API keys")
        return
    
    print(f"🧠 Using model: {openai_model}")
    print(f"🔍 Target: Jhajjar Power Plant")
    
    # Initialize vision client
    vision_client = VisionEnhancedOpenAIClient(openai_api_key, openai_model)
    print("✅ Vision client initialized")
    
    # Test 1: Simple text extraction
    print("\n📝 Test 1: Text Extraction")
    test_content = """
    Jhajjar Power Plant is operated by Apraava Energy Private Limited.
    The plant is located in Haryana, India and has a total capacity of 1320 MW.
    It consists of two supercritical coal-fired units of 660 MW each.
    The plant commenced commercial operations in 2012.
    """
    
    result = await vision_client.extract_field_text(
        field_name="organization_name",
        content=test_content,
        context="Jhajjar Power Plant organizational details"
    )
    
    if result:
        print(f"   ✅ Extracted: {result.extracted_value}")
        print(f"   📊 Confidence: {result.confidence_score:.2f}")
        print(f"   🔧 Method: {result.extraction_method}")
    else:
        print("   ❌ Text extraction failed")
    
    # Test 2: Search and scrape a real document
    print("\n🔍 Test 2: Real Document Search & Extraction")
    
    try:
        # Search for organizational documents
        async with SerpAPIClient(serp_api_key) as serp_client:
            search_orchestrator = PowerPlantSearchOrchestrator(serp_client)
            
            print("   🔍 Searching for Jhajjar Power Plant documents...")
            search_results = await search_orchestrator.search_specific_field(
                "Jhajjar Power Plant Apraava Energy company details", 
                max_results=3
            )
            
            print(f"   📄 Found {len(search_results)} search results")
            
            if search_results:
                # Try to scrape the first result
                first_result = search_results[0]
                print(f"   🌐 Scraping: {first_result.url}")
                
                async with ScraperAPIClient(scraper_api_key) as scraper_client:
                    content = await scraper_client.scrape_url(first_result.url)
                    
                    if content and content.content:
                        print(f"   ✅ Scraped {len(content.content)} characters")
                        
                        # Extract organization name from real content
                        result = await vision_client.extract_field_text(
                            field_name="organization_name",
                            content=content.content[:5000],  # Limit content
                            context="Jhajjar Power Plant organizational details from web"
                        )
                        
                        if result:
                            print(f"   ✅ Real extraction: {result.extracted_value}")
                            print(f"   📊 Confidence: {result.confidence_score:.2f}")
                        else:
                            print("   ❌ Real extraction failed")
                    else:
                        print("   ❌ Scraping failed")
            else:
                print("   ❌ No search results found")
                
    except Exception as e:
        print(f"   ❌ Search/scrape test failed: {e}")
    
    # Test 3: PDF type detection (simulated)
    print("\n📄 Test 3: PDF Type Detection")
    
    # Simulate different PDF types
    test_cases = [
        ("Rich text PDF", "This is a comprehensive technical document with detailed specifications and extensive content covering multiple aspects of the power plant operations and technical parameters." * 20),
        ("Sparse text PDF", "Limited content available in this document."),
        ("Scanned PDF", "")
    ]
    
    for pdf_type, text_content in test_cases:
        detected_type = vision_client.detect_pdf_type(b"dummy_pdf_bytes", text_content)
        print(f"   📋 {pdf_type}: Detected as '{detected_type}'")
    
    # Get usage statistics
    print("\n📊 Usage Statistics:")
    stats = vision_client.get_usage_stats()
    print(f"   🔢 Total extractions: {stats['total_extractions']}")
    print(f"   💰 Total tokens used: {stats['total_tokens_used']}")
    print(f"   🧠 Model: {stats['model']}")
    print(f"   👁️  Vision enabled: {stats['vision_enabled']}")
    
    # Close client
    await vision_client.close()
    print("\n✅ Vision-enhanced extraction test completed!")

async def main():
    """Main test function."""
    start_time = time.time()
    
    await test_vision_enhanced_extraction()
    
    duration = time.time() - start_time
    print(f"\n⏱️  Total test time: {duration:.1f} seconds")
    print("🎉 Vision-enhanced pipeline verification complete!")

if __name__ == "__main__":
    asyncio.run(main())
