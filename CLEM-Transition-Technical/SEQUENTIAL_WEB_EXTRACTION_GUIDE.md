# Sequential Web-Based Data Extraction Guide

## 🎯 Overview

This guide documents the **Sequential Web-Based Data Extraction Pipeline** that implements your requirements:

1. **Sequential workflow**: Organization level → Plant details → Unit level details
2. **Web search only**: No standard/mock values - everything sourced from web
3. **Source tracking**: Complete attribution for all scraped data
4. **Cache optimization**: Uses cached memory first, then web search for missing fields

## 🏗️ Architecture

### Core Components

1. **`SequentialWebExtractor`** - Main orchestrator class
2. **`PowerPlantSearchOrchestrator`** - Enhanced with new search methods
3. **`SourceTracker`** - Comprehensive source attribution
4. **Cache Management** - Intelligent caching with TTL

### Sequential Flow

```
📊 Phase 1: Organizational Details
    ↓ (cached data + web enhancement)
🏭 Phase 2: Plant Details  
    ↓ (cached data + web enhancement)
⚡ Phase 3: Unit Details
    ↓ (always web-sourced to replace mock data)
💾 Results + Source Tracking
```

## 🚀 Usage

### Basic Usage

```python
from src.sequential_web_extractor import SequentialWebExtractor

# Initialize extractor
extractor = SequentialWebExtractor(
    serp_api_key="your_serp_key",
    scraper_api_key="your_scraper_key", 
    groq_api_key="your_groq_key"
)

# Run sequential extraction
org_details, plant_details, unit_details, extraction_info = await extractor.extract_sequential_data(
    plant_name="Jhajjar Power Plant",
    force_refresh_org=False,      # Use cache if available
    force_refresh_plant=False,    # Use cache if available  
    force_refresh_units=True      # Always refresh units (replace mock data)
)

# Save results with source tracking
await extractor.save_results(
    org_details=org_details,
    plant_details=plant_details,
    unit_details=unit_details,
    extraction_info=extraction_info
)
```

### Demo Script

```bash
python demo_sequential_web_extraction.py
```

## 📊 Data Flow & Caching Strategy

### Phase 1: Organizational Details

**Cache Check** → **Web Search** → **LLM Extraction** → **Source Tracking** → **Cache Storage**

- **Cache location**: `cache/{plant_name}_org_details.json`
- **Web sources**: Comprehensive search across multiple categories
- **Fields extracted**: 9 organizational fields (cfpp_type, country_name, etc.)
- **Source tracking**: Web search queries, scraped URLs, confidence scores

### Phase 2: Plant Details

**Cache Check** → **Gap Analysis** → **Targeted Web Search** → **Enhancement** → **Cache Update**

- **Cache location**: `cache/{plant_name}_plant_details.json` 
- **Strategy**: Use cached data as base, fill missing fields via web search
- **Fields enhanced**: name, plant_type, coordinates, PPA details, grid connectivity
- **Source tracking**: Field-specific search queries and sources

### Phase 3: Unit Details

**Template Loading** → **Web Search per Field** → **LLM Extraction** → **Source Tracking** → **Cache Storage**

- **Template**: `unit_details.json` (92 fields per unit)
- **Strategy**: Always extract from web (replaces any mock data)
- **Search pattern**: `"{plant_name} Unit {unit_id} {field_name}"`
- **Source tracking**: Per-field source attribution with confidence scores

## 🔍 Source Tracking Features

### Comprehensive Attribution

Every extracted field includes:

```json
{
  "field_name": "capacity",
  "source_type": "web_search",
  "search_query": "Jhajjar Power Plant Unit 1 capacity",
  "source_url": "https://example.com/plant-details",
  "content_snippet": "The plant has two units of 660 MW each...",
  "confidence_score": 0.85,
  "extraction_timestamp": "2025-01-20T10:30:00Z"
}
```

### Source Categories

1. **Cached Sources** - Previously extracted data with original source lineage
2. **Web Search Sources** - Search queries and result counts
3. **Scraped Content Sources** - URLs, content snippets, extraction confidence
4. **LLM Processing Sources** - Model used, prompts, confidence scores

## 📁 Output Files

All results are saved with timestamps in `sequential_extraction_results/`:

```
jhajjar_power_plant_org_details_20250120_103000.json
jhajjar_power_plant_plant_details_20250120_103000.json  
jhajjar_power_plant_unit_details_20250120_103000.json
jhajjar_power_plant_extraction_info_20250120_103000.json
jhajjar_power_plant_sources_20250120_103000.json
```

## ⚙️ Configuration

### API Requirements

- **SERP API**: For web search functionality
- **Scraper API**: For content extraction
- **Groq API**: For LLM-based field extraction

### Rate Limiting

- **Search requests**: 1 second between searches
- **Scraping requests**: 0.5 seconds between scrapes  
- **LLM requests**: 0.3 seconds between extractions

### Confidence Thresholds

- **Organizational fields**: 0.6 minimum confidence
- **Plant fields**: 0.6 minimum confidence
- **Unit fields**: 0.5 minimum confidence (lower due to specificity)

## 🎛️ Advanced Features

### Force Refresh Options

```python
# Force refresh all data
await extractor.extract_sequential_data(
    plant_name="Plant Name",
    force_refresh_org=True,    # Skip org cache
    force_refresh_plant=True,  # Skip plant cache
    force_refresh_units=True   # Skip unit cache
)
```

### Cache Management

```python
# Check cache status
cached_org = extractor._get_cached_org_details("Plant Name")
cached_plant = plant_cache.get_plant_details("Plant Name")

# Manual cache clearing
plant_cache.clear_cache()
```

### Source Analysis

```python
# Get detailed source summary
source_summary = extractor.source_tracker.get_all_sources_summary()

# Source statistics
stats = source_summary['source_statistics']
print(f"Web searches: {stats['web_searches']}")
print(f"Scraped pages: {stats['scraped_pages']}")
print(f"Cached fields: {stats['cached_fields']}")
```

## 🧪 Testing

### Unit Tests

```bash
python test_sequential_extractor.py
```

### Integration Tests

```bash
python demo_sequential_web_extraction.py
```

## 🔧 Troubleshooting

### Common Issues

1. **Missing API Keys**: Set environment variables or update config
2. **Rate Limiting**: Increase delays in configuration
3. **Low Confidence**: Adjust thresholds or improve search queries
4. **Cache Issues**: Clear cache and force refresh

### Debug Mode

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📈 Performance Metrics

### Typical Extraction Times

- **Organizational Phase**: 30-60 seconds (5-10 searches)
- **Plant Phase**: 15-30 seconds (cached) or 45-90 seconds (full)
- **Unit Phase**: 60-120 seconds per unit (field-by-field extraction)

### Success Rates

- **Organizational Fields**: 85-95% completion
- **Plant Fields**: 90-98% completion (with cache)
- **Unit Fields**: 70-85% completion (varies by plant documentation)

## 🎯 Next Steps

1. **Run the demo** to see the system in action
2. **Review source tracking** reports for data lineage
3. **Customize field templates** for specific requirements
4. **Integrate with existing workflows** using the API

---

**Ready to extract real data with full source attribution! 🚀**
