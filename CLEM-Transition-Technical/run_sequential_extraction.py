#!/usr/bin/env python3
"""
Simple script to run sequential web extraction for any power plant.
Usage: python run_sequential_extraction.py "Plant Name"
"""

import asyncio
import sys
import os
from datetime import datetime
from unittest.mock import MagicMock

# Mock problematic dependencies before importing
sys.modules['src.openai_client'] = MagicMock()

from src.sequential_web_extractor import SequentialWebExtractor
from src.config import config

async def run_extraction(plant_name: str, force_refresh: bool = False):
    """Run sequential extraction for a specific plant."""
    
    print(f"🚀 Starting Sequential Web Extraction")
    print(f"🏭 Plant: {plant_name}")
    print(f"🔄 Force Refresh: {force_refresh}")
    print("=" * 60)
    
    try:
        # Initialize the sequential extractor
        extractor = SequentialWebExtractor(
            serp_api_key=config.pipeline.scraper_api_key,  # Using ScraperAPI for search
            scraper_api_key=config.pipeline.scraper_api_key,
            groq_api_key=config.pipeline.groq_api_key
        )
        
        # Run sequential extraction
        start_time = datetime.now()
        
        org_details, plant_details, unit_details, extraction_info = await extractor.extract_sequential_data(
            plant_name=plant_name,
            force_refresh_org=force_refresh,
            force_refresh_plant=force_refresh,
            force_refresh_units=True  # Always refresh units to replace mock data
        )
        
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        # Display results
        print(f"\n✅ Extraction completed in {duration:.1f} seconds!")
        print(f"📊 Organizational fields: {len([v for v in org_details.values() if v not in [None, '', []]])}")
        print(f"🏭 Plant fields: {len([v for v in plant_details.values() if v not in [None, '', []]])}")
        print(f"⚡ Unit details: {len(unit_details.get('units', []))} units extracted")
        
        # Save results
        await extractor.save_results(
            org_details=org_details,
            plant_details=plant_details,
            unit_details=unit_details,
            extraction_info=extraction_info
        )
        
        print(f"📁 Results saved to: sequential_extraction_results/")
        return True
        
    except Exception as e:
        print(f"❌ Extraction failed: {e}")
        return False

def main():
    """Main function."""
    if len(sys.argv) < 2:
        print("Usage: python run_sequential_extraction.py \"Plant Name\" [--force-refresh]")
        print("Example: python run_sequential_extraction.py \"Jhajjar Power Plant\"")
        print("Example: python run_sequential_extraction.py \"Vogtle Nuclear Plant\" --force-refresh")
        sys.exit(1)
    
    plant_name = sys.argv[1]
    force_refresh = "--force-refresh" in sys.argv
    
    # Check API keys
    if not config.pipeline.scraper_api_key:
        print("❌ SCRAPER_API_KEY not found in environment variables")
        sys.exit(1)
    
    if not config.pipeline.groq_api_key:
        print("❌ GROQ_API_KEY not found in environment variables")
        sys.exit(1)
    
    # Run extraction
    success = asyncio.run(run_extraction(plant_name, force_refresh))
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
