<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradients -->
    <linearGradient id="searchGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#45a049;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="scrapingGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#2196F3;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1976D2;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="htmlGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#FF9800;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#F57C00;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="pdfGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#E91E63;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#C2185B;stop-opacity:1" />
    </linearGradient>
    
    <linearGradient id="validationGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#9C27B0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#7B1FA2;stop-opacity:1" />
    </linearGradient>
    
    <!-- Arrow marker -->
    <defs>
      <marker id="arrowhead" markerWidth="10" markerHeight="7" 
              refX="9" refY="3.5" orient="auto">
        <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
      </marker>
    </defs>
  </defs>
  
  <!-- Background -->
  <rect width="1400" height="1000" fill="#f8f9fa" stroke="#e9ecef" stroke-width="2"/>
  
  <!-- Title -->
  <text x="700" y="30" text-anchor="middle" font-family="Arial, sans-serif" font-size="24" font-weight="bold" fill="#2c3e50">
    Power Plant Data Extraction Pipeline - HTML-First Strategy
  </text>
  
  <!-- Input -->
  <rect x="50" y="70" width="120" height="60" rx="10" fill="#34495e" stroke="#2c3e50" stroke-width="2"/>
  <text x="110" y="95" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="white">Plant Name</text>
  <text x="110" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Input</text>
  
  <!-- Phase 1: Search -->
  <rect x="220" y="50" width="200" height="100" rx="10" fill="url(#searchGradient)" stroke="#4CAF50" stroke-width="2"/>
  <text x="320" y="75" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">Phase 1: Search</text>
  <text x="320" y="95" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">SERP API</text>
  <text x="320" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">• Basic Discovery</text>
  <text x="320" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">• Organizational</text>
  <text x="320" y="140" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">• Technical • Location • PPA</text>
  
  <!-- Phase 2: Scraping -->
  <rect x="470" y="50" width="200" height="100" rx="10" fill="url(#scrapingGradient)" stroke="#2196F3" stroke-width="2"/>
  <text x="570" y="75" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">Phase 2: Scraping</text>
  <text x="570" y="95" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">ScraperAPI + PDF Processor</text>
  <text x="570" y="110" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">• Content Detection</text>
  <text x="570" y="125" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">• HTML Processing</text>
  <text x="570" y="140" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">• PDF Processing</text>
  
  <!-- Content Separation -->
  <rect x="720" y="30" width="160" height="70" rx="8" fill="#FFF3E0" stroke="#FF9800" stroke-width="2"/>
  <text x="800" y="50" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#E65100">Content Separation</text>
  <text x="800" y="65" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#E65100">HTML vs PDF Detection</text>
  <text x="800" y="80" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#E65100">Source Prioritization</text>
  <text x="800" y="95" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#E65100">Quality Analysis</text>
  
  <!-- HTML Sources -->
  <rect x="720" y="120" width="160" height="80" rx="8" fill="#FFF3E0" stroke="#FF9800" stroke-width="2"/>
  <text x="800" y="140" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#E65100">HTML Sources</text>
  <text x="800" y="155" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#E65100">• Company Websites</text>
  <text x="800" y="170" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#E65100">• News Articles</text>
  <text x="800" y="185" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#E65100">• Industry Reports</text>
  
  <!-- PDF Sources -->
  <rect x="720" y="220" width="160" height="80" rx="8" fill="#FCE4EC" stroke="#E91E63" stroke-width="2"/>
  <text x="800" y="240" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#AD1457">PDF Sources</text>
  <text x="800" y="255" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#AD1457">• Annual Reports</text>
  <text x="800" y="270" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#AD1457">• Financial Statements</text>
  <text x="800" y="285" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#AD1457">• Technical Documents</text>
  
  <!-- Phase 3A: HTML-First Extraction -->
  <rect x="50" y="350" width="200" height="120" rx="10" fill="url(#htmlGradient)" stroke="#FF9800" stroke-width="2"/>
  <text x="150" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">Phase 3A: HTML-First</text>
  <text x="150" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">Groq LLM Extraction</text>
  <text x="150" y="415" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Extract all 9 fields:</text>
  <text x="150" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">cfpp_type, organization_name</text>
  <text x="150" y="445" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">country, province, plants_count</text>
  <text x="150" y="460" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">plant_types, ppa_flag, currency, fy</text>
  
  <!-- Gap Analysis -->
  <rect x="300" y="350" width="180" height="120" rx="10" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2"/>
  <text x="390" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2E7D32">Gap Analysis</text>
  <text x="390" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#2E7D32">Confidence Evaluation</text>
  <text x="390" y="415" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2E7D32">Thresholds:</text>
  <text x="390" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#2E7D32">organization_name: 0.8</text>
  <text x="390" y="445" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#2E7D32">financial_year: 0.6</text>
  <text x="390" y="460" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#2E7D32">ppa_flag: 0.5</text>
  
  <!-- Phase 3B: Targeted PDF Processing -->
  <rect x="530" y="350" width="200" height="120" rx="10" fill="url(#pdfGradient)" stroke="#E91E63" stroke-width="2"/>
  <text x="630" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">Phase 3B: Targeted PDF</text>
  <text x="630" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">Only Missing Fields</text>
  <text x="630" y="415" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">Smart PDF Selection:</text>
  <text x="630" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">financial_year → Annual Reports</text>
  <text x="630" y="445" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">currency_in → Financial Docs</text>
  <text x="630" y="460" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="white">ppa_flag → Contract Docs</text>
  
  <!-- Intelligent Merging -->
  <rect x="780" y="350" width="180" height="120" rx="10" fill="#F3E5F5" stroke="#9C27B0" stroke-width="2"/>
  <text x="870" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#6A1B9A">Intelligent Merging</text>
  <text x="870" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#6A1B9A">Confidence Comparison</text>
  <text x="870" y="415" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#6A1B9A">Rules:</text>
  <text x="870" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#6A1B9A">Use PDF if confidence</text>
  <text x="870" y="445" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#6A1B9A">> HTML + 0.2</text>
  <text x="870" y="460" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#6A1B9A">Source tracking</text>
  
  <!-- Phase 4: Validation & Output -->
  <rect x="1020" y="350" width="200" height="120" rx="10" fill="url(#validationGradient)" stroke="#9C27B0" stroke-width="2"/>
  <text x="1120" y="375" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="white">Phase 4: Validation</text>
  <text x="1120" y="395" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="white">Cross-Source Validation</text>
  <text x="1120" y="415" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">• Data Model Validation</text>
  <text x="1120" y="430" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">• Field Format Checking</text>
  <text x="1120" y="445" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">• JSON Output Generation</text>
  <text x="1120" y="460" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="white">• org_details.json</text>
  
  <!-- Strategy Selection Logic -->
  <rect x="50" y="520" width="1170" height="80" rx="10" fill="#E3F2FD" stroke="#1976D2" stroke-width="2"/>
  <text x="635" y="545" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#0D47A1">Adaptive Strategy Selection</text>
  <text x="200" y="565" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#1565C0">1. HTML-First Strategy</text>
  <text x="200" y="580" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#1565C0">PDFs available + 3+ sources</text>
  <text x="450" y="565" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#1565C0">2. Enhanced Multi-Source</text>
  <text x="450" y="580" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#1565C0">High quality + 10K+ chars</text>
  <text x="700" y="565" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#1565C0">3. Basic Extraction</text>
  <text x="700" y="580" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#1565C0">2+ sources available</text>
  <text x="950" y="565" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#1565C0">4. Single-Source</text>
  <text x="950" y="580" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#1565C0">Limited content fallback</text>
  
  <!-- Performance Metrics -->
  <rect x="50" y="630" width="280" height="120" rx="10" fill="#E8F5E8" stroke="#4CAF50" stroke-width="2"/>
  <text x="190" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#2E7D32">Performance Improvements</text>
  <text x="190" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#2E7D32">HTML-First vs Traditional:</text>
  <text x="190" y="695" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2E7D32">• Token Usage: 40-60% reduction</text>
  <text x="190" y="710" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2E7D32">• Processing Time: 30-50% faster</text>
  <text x="190" y="725" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2E7D32">• API Calls: 50-70% fewer</text>
  <text x="190" y="740" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#2E7D32">• Cost: Major savings</text>
  
  <!-- Technical Components -->
  <rect x="360" y="630" width="280" height="120" rx="10" fill="#FFF3E0" stroke="#FF9800" stroke-width="2"/>
  <text x="500" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#E65100">Technical Components</text>
  <text x="500" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#E65100">New Modules:</text>
  <text x="500" y="695" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#E65100">• HtmlFirstExtractor</text>
  <text x="500" y="710" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#E65100">• FieldExtractionSummary</text>
  <text x="500" y="725" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#E65100">• Enhanced AdaptiveExtractor</text>
  <text x="500" y="740" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#E65100">• PDF Processor Integration</text>
  
  <!-- Data Flow -->
  <rect x="670" y="630" width="280" height="120" rx="10" fill="#FCE4EC" stroke="#E91E63" stroke-width="2"/>
  <text x="810" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#AD1457">Data Flow Features</text>
  <text x="810" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#AD1457">Smart Processing:</text>
  <text x="810" y="695" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#AD1457">• Content type detection</text>
  <text x="810" y="710" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#AD1457">• Confidence-based decisions</text>
  <text x="810" y="725" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#AD1457">• Field-specific PDF targeting</text>
  <text x="810" y="740" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#AD1457">• Source tracking & merging</text>
  
  <!-- Output Fields -->
  <rect x="980" y="630" width="280" height="120" rx="10" fill="#F3E5F5" stroke="#9C27B0" stroke-width="2"/>
  <text x="1120" y="655" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#6A1B9A">Output Fields</text>
  <text x="1120" y="675" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#6A1B9A">OrganizationalDetails:</text>
  <text x="1120" y="695" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#6A1B9A">• cfpp_type, organization_name</text>
  <text x="1120" y="710" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#6A1B9A">• country_name, province</text>
  <text x="1120" y="725" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#6A1B9A">• plants_count, plant_types</text>
  <text x="1120" y="740" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#6A1B9A">• ppa_flag, currency_in, financial_year</text>
  
  <!-- RAG vs Current Approach -->
  <rect x="50" y="780" width="600" height="80" rx="10" fill="#FFEBEE" stroke="#F44336" stroke-width="2"/>
  <text x="350" y="805" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#C62828">Current Approach vs RAG</text>
  <text x="200" y="825" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#D32F2F">Current: Priority-based Processing</text>
  <text x="200" y="840" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#D32F2F">• Source type prioritization</text>
  <text x="200" y="855" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#D32F2F">• Linear content combination</text>
  <text x="500" y="825" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#D32F2F">Not Using RAG:</text>
  <text x="500" y="840" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#D32F2F">• No vector embeddings</text>
  <text x="500" y="855" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#D32F2F">• No semantic search</text>
  
  <!-- Future Enhancements -->
  <rect x="680" y="780" width="600" height="80" rx="10" fill="#E0F2F1" stroke="#009688" stroke-width="2"/>
  <text x="980" y="805" text-anchor="middle" font-family="Arial, sans-serif" font-size="14" font-weight="bold" fill="#00695C">Future Enhancements</text>
  <text x="850" y="825" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#00796B">Potential RAG Integration:</text>
  <text x="850" y="840" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#00796B">• Vector-based content retrieval</text>
  <text x="850" y="855" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#00796B">• Semantic similarity matching</text>
  <text x="1110" y="825" text-anchor="middle" font-family="Arial, sans-serif" font-size="11" fill="#00796B">Advanced Features:</text>
  <text x="1110" y="840" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#00796B">• Document chunking</text>
  <text x="1110" y="855" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#00796B">• Context-aware extraction</text>
  
  <!-- Arrows -->
  <!-- Input to Search -->
  <line x1="170" y1="100" x2="220" y2="100" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Search to Scraping -->
  <line x1="420" y1="100" x2="470" y2="100" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Scraping to Content Separation -->
  <line x1="670" y1="100" x2="720" y2="65" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Content Separation to HTML Sources -->
  <line x1="800" y1="100" x2="800" y2="120" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Content Separation to PDF Sources -->
  <line x1="800" y1="100" x2="800" y2="220" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- HTML Sources to HTML-First Extraction -->
  <line x1="720" y1="160" x2="250" y2="350" stroke="#FF9800" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- HTML-First to Gap Analysis -->
  <line x1="250" y1="410" x2="300" y2="410" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Gap Analysis to Targeted PDF (conditional) -->
  <line x1="480" y1="410" x2="530" y2="410" stroke="#E91E63" stroke-width="3" stroke-dasharray="5,5" marker-end="url(#arrowhead)"/>
  <text x="505" y="400" text-anchor="middle" font-family="Arial, sans-serif" font-size="9" fill="#E91E63">if gaps found</text>
  
  <!-- PDF Sources to Targeted PDF Processing -->
  <line x1="720" y1="260" x2="630" y2="350" stroke="#E91E63" stroke-width="3" marker-end="url(#arrowhead)"/>
  
  <!-- Both extractions to Merging -->
  <line x1="250" y1="470" x2="780" y2="410" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  <line x1="730" y1="410" x2="780" y2="410" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Merging to Validation -->
  <line x1="960" y1="410" x2="1020" y2="410" stroke="#333" stroke-width="2" marker-end="url(#arrowhead)"/>
  
  <!-- Token savings indicator -->
  <text x="400" y="320" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" font-weight="bold" fill="#4CAF50">40-60% Token Savings!</text>
  <text x="400" y="335" text-anchor="middle" font-family="Arial, sans-serif" font-size="10" fill="#4CAF50">Smart targeting reduces LLM calls</text>
  
</svg>
