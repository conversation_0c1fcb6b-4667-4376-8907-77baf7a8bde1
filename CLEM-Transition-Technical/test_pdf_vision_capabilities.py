"""
Test PDF Vision Capabilities
Demonstrates the vision-enhanced pipeline's ability to process different types of PDFs.
"""

import asyncio
import json
import os
import requests
from datetime import datetime

from src.vision_enhanced_openai_client import VisionEnhancedOpenAIClient
from src.vision_enhanced_pdf_processor import VisionEnhancedPDFProcessor

async def test_pdf_vision_capabilities():
    """Test vision capabilities on real PDF documents."""
    
    print("🧪 TESTING PDF VISION CAPABILITIES")
    print("=" * 50)
    
    # Get API keys
    openai_api_key = os.getenv("OPENAI_API_KEY")
    openai_model = os.getenv("OPENAI_MODEL", "gpt-4o")
    
    if not openai_api_key:
        print("❌ Missing OpenAI API key")
        return
    
    # Initialize vision components
    vision_client = VisionEnhancedOpenAIClient(openai_api_key, openai_model)
    pdf_processor = VisionEnhancedPDFProcessor(vision_client)
    
    print(f"🧠 Model: {openai_model}")
    print(f"👁️  Vision capabilities: ENABLED")
    
    # Test PDFs from our previous extractions
    test_pdfs = [
        {
            "name": "Jhajjar Annual Report 2021-22",
            "url": "https://www.apraava.com/getmedia/050c2654-47da-4bd1-89ad-1e4aecb4b1f2/Jhajjar-Power-Limited-Annual-Report_2021-22.pdf",
            "expected_fields": ["organization_name", "capacity", "technology"]
        }
    ]
    
    for i, pdf_info in enumerate(test_pdfs, 1):
        print(f"\n📄 Test {i}: {pdf_info['name']}")
        print(f"🌐 URL: {pdf_info['url']}")
        
        try:
            # Download PDF
            print("   📥 Downloading PDF...")
            response = requests.get(pdf_info['url'], timeout=30)
            
            if response.status_code == 200:
                pdf_bytes = response.content
                print(f"   ✅ Downloaded {len(pdf_bytes)} bytes")
                
                # Analyze PDF type
                text_content, title, metadata = pdf_processor.extract_text_and_metadata_enhanced(
                    pdf_bytes, pdf_info['url']
                )
                
                print(f"   📊 Document analysis:")
                print(f"      Type: {metadata.get('document_type', 'unknown')}")
                print(f"      Pages: {metadata.get('total_pages', 0)}")
                print(f"      Text extraction: {'✅' if metadata.get('text_extraction_success') else '❌'}")
                print(f"      Processing method: {metadata.get('processing_method', 'unknown')}")
                
                if text_content:
                    print(f"      Text length: {len(text_content)} characters")
                    print(f"      Sample: {text_content[:100]}...")
                
                # Test vision recommendation
                vision_recommended = pdf_processor.is_vision_extraction_recommended(pdf_bytes, pdf_info['url'])
                print(f"      Vision recommended: {'✅' if vision_recommended else '❌'}")
                
                # Test field extraction
                print(f"   🔍 Testing field extraction:")
                
                for field_name in pdf_info['expected_fields']:
                    print(f"      🔍 Extracting {field_name}...")
                    
                    # Use hybrid extraction (text + vision fallback)
                    result = await pdf_processor.extract_field_with_vision_fallback(
                        field_name=field_name,
                        pdf_bytes=pdf_bytes,
                        context=f"Power plant details from {pdf_info['name']}",
                        url=pdf_info['url']
                    )
                    
                    if result:
                        print(f"         ✅ Value: {str(result.extracted_value)[:50]}{'...' if len(str(result.extracted_value)) > 50 else ''}")
                        print(f"         📊 Confidence: {result.confidence_score:.2f}")
                        print(f"         🔧 Method: {result.extraction_method}")
                        print(f"         📄 Document type: {result.document_type}")
                    else:
                        print(f"         ❌ Extraction failed")
                    
                    await asyncio.sleep(1)  # Rate limiting
                
            else:
                print(f"   ❌ Download failed: HTTP {response.status_code}")
                
        except Exception as e:
            print(f"   ❌ Test failed: {e}")
            continue
    
    # Test document type detection with simulated PDFs
    print(f"\n🔍 Testing Document Type Detection:")
    
    test_cases = [
        ("Rich Text PDF", "This is a comprehensive technical document with detailed specifications and extensive content covering multiple aspects of the power plant operations, technical parameters, financial information, regulatory compliance, environmental impact assessments, and operational performance metrics. The document contains detailed tables, charts, and technical diagrams that provide in-depth analysis of the facility's capabilities and performance characteristics." * 10),
        ("Standard Text PDF", "Jhajjar Power Plant is a coal-fired power station located in Haryana, India. The plant has a total capacity of 1320 MW consisting of two units of 660 MW each. It is operated by Apraava Energy and uses supercritical technology for improved efficiency."),
        ("Sparse Text PDF", "Limited content available."),
        ("Scanned PDF", "")
    ]
    
    for case_name, text_content in test_cases:
        detected_type = vision_client.detect_pdf_type(b"dummy_pdf_bytes", text_content)
        print(f"   📋 {case_name}: Detected as '{detected_type}'")
    
    # Get usage statistics
    print(f"\n📊 Usage Statistics:")
    stats = vision_client.get_usage_stats()
    print(f"   🔢 Total extractions: {stats['total_extractions']}")
    print(f"   👁️  Vision extractions: {stats['total_vision_extractions']}")
    print(f"   📝 Text extractions: {stats['total_text_extractions']}")
    print(f"   💰 Total tokens used: {stats['total_tokens_used']}")
    print(f"   🧠 Model: {stats['model']}")
    
    # Close clients
    await vision_client.close()
    
    print(f"\n✅ PDF Vision Capabilities Test Completed!")

async def main():
    """Main test function."""
    
    print("🚀 PDF VISION CAPABILITIES TEST")
    print("=" * 50)
    print("🔥 Testing:")
    print("   📄 Real PDF document processing")
    print("   👁️  Vision vs text extraction")
    print("   📊 Document type detection")
    print("   🔄 Hybrid extraction strategies")
    print("=" * 50)
    
    await test_pdf_vision_capabilities()
    
    print("\n🎉 All PDF vision tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
