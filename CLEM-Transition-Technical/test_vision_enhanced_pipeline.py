"""
Test script for Vision-Enhanced OpenAI Pipeline.
Verifies that the vision capabilities work correctly.
"""

import asyncio
import os
import json
from src.vision_enhanced_openai_client import VisionEnhancedOpenAIClient
from src.vision_enhanced_pdf_processor import VisionEnhancedPDFProcessor

async def test_vision_client():
    """Test the vision-enhanced OpenAI client."""
    print("🧪 Testing Vision-Enhanced OpenAI Client...")
    
    # Get API key
    openai_api_key = os.getenv("OPENAI_API_KEY")
    if not openai_api_key:
        print("❌ OPENAI_API_KEY not found in environment")
        return False
    
    try:
        # Initialize client
        client = VisionEnhancedOpenAIClient(openai_api_key, "gpt-4o-mini")
        print("✅ Vision client initialized successfully")
        
        # Test text extraction
        test_content = """
        Jhajjar Power Plant Technical Specifications:
        - Total Capacity: 1320 MW
        - Unit 1 Capacity: 660 MW
        - Unit 2 Capacity: 660 MW
        - Technology: Supercritical Coal-fired
        - Commercial Operation Date: April 2012
        """
        
        result = await client.extract_field_text(
            field_name="capacity",
            content=test_content,
            context="Test extraction for Jhajjar Power Plant Unit 1"
        )
        
        if result and result.extracted_value:
            print(f"✅ Text extraction test passed: {result.extracted_value} (confidence: {result.confidence_score:.2f})")
        else:
            print("❌ Text extraction test failed")
            return False
        
        # Test PDF type detection
        test_pdf_bytes = b"dummy_pdf_content"  # This would be real PDF bytes in practice
        pdf_type = client.detect_pdf_type(test_pdf_bytes, "dummy text content with sufficient length for testing")
        print(f"✅ PDF type detection test: {pdf_type}")
        
        # Get usage stats
        stats = client.get_usage_stats()
        print(f"✅ Usage stats: {stats['total_extractions']} extractions, {stats['total_tokens_used']} tokens")
        
        await client.close()
        return True
        
    except Exception as e:
        print(f"❌ Vision client test failed: {e}")
        return False

async def test_pdf_processor():
    """Test the vision-enhanced PDF processor."""
    print("\n🧪 Testing Vision-Enhanced PDF Processor...")
    
    try:
        # Initialize without vision client first
        processor = VisionEnhancedPDFProcessor()
        print("✅ PDF processor initialized successfully (without vision)")
        
        # Test document classification
        test_text = "This is a test document with sufficient content for classification testing. " * 20
        test_pdf_bytes = b"dummy_pdf_content"
        
        doc_type = processor._classify_document_type(test_text, test_pdf_bytes)
        print(f"✅ Document classification test: {doc_type}")
        
        # Test processing stats
        stats = processor.get_processing_stats()
        print(f"✅ Processing stats: vision_enabled={stats['vision_enabled']}")
        
        return True
        
    except Exception as e:
        print(f"❌ PDF processor test failed: {e}")
        return False

async def test_integration():
    """Test integration between components."""
    print("\n🧪 Testing Component Integration...")
    
    openai_api_key = os.getenv("OPENAI_API_KEY")
    if not openai_api_key:
        print("❌ OPENAI_API_KEY not found - skipping integration test")
        return False
    
    try:
        # Initialize integrated components
        vision_client = VisionEnhancedOpenAIClient(openai_api_key, "gpt-4o-mini")
        pdf_processor = VisionEnhancedPDFProcessor(vision_client)
        
        print("✅ Integrated components initialized successfully")
        
        # Test that PDF processor recognizes vision capabilities
        stats = pdf_processor.get_processing_stats()
        if stats['vision_enabled']:
            print("✅ PDF processor correctly detects vision capabilities")
        else:
            print("❌ PDF processor does not detect vision capabilities")
            return False
        
        # Test vision recommendation
        test_pdf_bytes = b"dummy_pdf_content"
        recommendation = pdf_processor.is_vision_extraction_recommended(test_pdf_bytes, "test.pdf")
        print(f"✅ Vision recommendation test: {recommendation}")
        
        await vision_client.close()
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def test_dependencies():
    """Test that all required dependencies are available."""
    print("🧪 Testing Dependencies...")
    
    dependencies = [
        ("json", "json"),
        ("asyncio", "asyncio"),
        ("logging", "logging"),
        ("datetime", "datetime"),
        ("typing", "typing"),
        ("dataclasses", "dataclasses"),
        ("openai", "openai"),
        ("base64", "base64"),
        ("io", "io")
    ]
    
    optional_dependencies = [
        ("pdf2image", "pdf2image"),
        ("PIL", "Pillow"),
        ("fitz", "PyMuPDF")
    ]
    
    all_good = True
    
    # Test core dependencies
    for name, package in dependencies:
        try:
            __import__(name)
            print(f"✅ {package}: Available")
        except ImportError:
            print(f"❌ {package}: Missing")
            all_good = False
    
    # Test optional dependencies (for vision features)
    vision_available = True
    for name, package in optional_dependencies:
        try:
            __import__(name)
            print(f"✅ {package}: Available (vision features enabled)")
        except ImportError:
            print(f"⚠️  {package}: Missing (vision features will be limited)")
            vision_available = False
    
    if vision_available:
        print("🔥 All vision dependencies available - full multimodal capabilities enabled!")
    else:
        print("⚠️  Some vision dependencies missing - install with: pip install pdf2image PyMuPDF Pillow")
    
    return all_good

async def main():
    """Run all tests."""
    print("🚀 VISION-ENHANCED PIPELINE TEST SUITE")
    print("=" * 50)
    
    # Test dependencies first
    deps_ok = test_dependencies()
    
    if not deps_ok:
        print("\n❌ Core dependencies missing - please install requirements.txt")
        return
    
    # Test individual components
    client_ok = await test_vision_client()
    processor_ok = await test_pdf_processor()
    integration_ok = await test_integration()
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY:")
    print(f"   Dependencies: {'✅ PASS' if deps_ok else '❌ FAIL'}")
    print(f"   Vision Client: {'✅ PASS' if client_ok else '❌ FAIL'}")
    print(f"   PDF Processor: {'✅ PASS' if processor_ok else '❌ FAIL'}")
    print(f"   Integration: {'✅ PASS' if integration_ok else '❌ FAIL'}")
    
    if all([deps_ok, client_ok, processor_ok, integration_ok]):
        print("\n🎉 ALL TESTS PASSED - Vision-Enhanced Pipeline Ready!")
        print("🔥 You can now run: python run_vision_enhanced_openai_pipeline.py")
    else:
        print("\n❌ Some tests failed - please check the issues above")
        print("💡 Install missing dependencies with: pip install -r requirements.txt")

if __name__ == "__main__":
    asyncio.run(main())
