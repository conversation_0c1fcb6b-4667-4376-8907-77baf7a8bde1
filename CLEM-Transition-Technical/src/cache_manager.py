"""
Cache Manager for Power Plant Data Pipeline
Handles caching of plant details and other extracted data for reuse in subsequent extractions.
"""
import json
import os
import pickle
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List
from pathlib import Path

logger = logging.getLogger(__name__)


class PlantDataCache:
    """Cache manager for plant data with persistence and expiration."""
    
    def __init__(self, cache_dir: str = "cache", cache_ttl_hours: int = 24):
        """
        Initialize the cache manager.
        
        Args:
            cache_dir: Directory to store cache files
            cache_ttl_hours: Time-to-live for cache entries in hours
        """
        self.cache_dir = Path(cache_dir)
        self.cache_ttl = timedelta(hours=cache_ttl_hours)
        self.memory_cache = {}
        
        # Create cache directory if it doesn't exist
        self.cache_dir.mkdir(exist_ok=True)
        
        # Initialize cache metadata
        self.metadata_file = self.cache_dir / "cache_metadata.json"
        self.metadata = self._load_metadata()
        
        logger.info(f"Cache manager initialized with TTL: {cache_ttl_hours} hours")
    
    def _load_metadata(self) -> Dict[str, Any]:
        """Load cache metadata from disk."""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Failed to load cache metadata: {e}")
        
        return {
            "entries": {},
            "created": datetime.now().isoformat(),
            "last_cleanup": datetime.now().isoformat()
        }
    
    def _save_metadata(self):
        """Save cache metadata to disk."""
        try:
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, indent=2, ensure_ascii=False)
        except Exception as e:
            logger.error(f"Failed to save cache metadata: {e}")
    
    def _get_cache_key(self, plant_name: str, data_type: str) -> str:
        """Generate cache key for plant data."""
        return f"{plant_name.lower().replace(' ', '_')}_{data_type}"
    
    def _get_cache_file(self, cache_key: str) -> Path:
        """Get cache file path for a given key."""
        return self.cache_dir / f"{cache_key}.json"
    
    def _is_expired(self, cache_key: str) -> bool:
        """Check if cache entry is expired."""
        if cache_key not in self.metadata["entries"]:
            return True
        
        created_time = datetime.fromisoformat(self.metadata["entries"][cache_key]["created"])
        return datetime.now() - created_time > self.cache_ttl
    
    def store_plant_details(self, plant_name: str, plant_details: Dict[str, Any]) -> str:
        """
        Store plant details in cache.
        
        Args:
            plant_name: Name of the power plant
            plant_details: Plant details dictionary
            
        Returns:
            Cache key for the stored data
        """
        cache_key = self._get_cache_key(plant_name, "plant_details")
        cache_file = self._get_cache_file(cache_key)
        
        try:
            # Store in memory cache
            self.memory_cache[cache_key] = plant_details
            
            # Store on disk
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(plant_details, f, indent=2, ensure_ascii=False)
            
            # Update metadata
            self.metadata["entries"][cache_key] = {
                "plant_name": plant_name,
                "data_type": "plant_details",
                "created": datetime.now().isoformat(),
                "file_path": str(cache_file),
                "size_bytes": cache_file.stat().st_size
            }
            self._save_metadata()
            
            logger.info(f"Plant details cached for {plant_name} with key: {cache_key}")
            return cache_key
            
        except Exception as e:
            logger.error(f"Failed to cache plant details for {plant_name}: {e}")
            raise
    
    def get_plant_details(self, plant_name: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve plant details from cache.
        
        Args:
            plant_name: Name of the power plant
            
        Returns:
            Plant details dictionary or None if not found/expired
        """
        cache_key = self._get_cache_key(plant_name, "plant_details")
        
        # Check if expired
        if self._is_expired(cache_key):
            logger.info(f"Cache entry expired for {plant_name}")
            self._remove_entry(cache_key)
            return None
        
        # Try memory cache first
        if cache_key in self.memory_cache:
            logger.info(f"Plant details retrieved from memory cache for {plant_name}")
            return self.memory_cache[cache_key]
        
        # Try disk cache
        cache_file = self._get_cache_file(cache_key)
        if cache_file.exists():
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    plant_details = json.load(f)
                
                # Store in memory cache for faster access
                self.memory_cache[cache_key] = plant_details
                
                logger.info(f"Plant details retrieved from disk cache for {plant_name}")
                return plant_details
                
            except Exception as e:
                logger.error(f"Failed to load cached plant details for {plant_name}: {e}")
                self._remove_entry(cache_key)
        
        return None
    
    def store_unit_details(self, plant_name: str, unit_details: Dict[str, Any]) -> str:
        """
        Store unit details in cache.
        
        Args:
            plant_name: Name of the power plant
            unit_details: Unit details dictionary
            
        Returns:
            Cache key for the stored data
        """
        cache_key = self._get_cache_key(plant_name, "unit_details")
        cache_file = self._get_cache_file(cache_key)
        
        try:
            # Store in memory cache
            self.memory_cache[cache_key] = unit_details
            
            # Store on disk
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(unit_details, f, indent=2, ensure_ascii=False)
            
            # Update metadata
            self.metadata["entries"][cache_key] = {
                "plant_name": plant_name,
                "data_type": "unit_details",
                "created": datetime.now().isoformat(),
                "file_path": str(cache_file),
                "size_bytes": cache_file.stat().st_size
            }
            self._save_metadata()
            
            logger.info(f"Unit details cached for {plant_name} with key: {cache_key}")
            return cache_key
            
        except Exception as e:
            logger.error(f"Failed to cache unit details for {plant_name}: {e}")
            raise
    
    def get_unit_details(self, plant_name: str) -> Optional[Dict[str, Any]]:
        """
        Retrieve unit details from cache.
        
        Args:
            plant_name: Name of the power plant
            
        Returns:
            Unit details dictionary or None if not found/expired
        """
        cache_key = self._get_cache_key(plant_name, "unit_details")
        
        # Check if expired
        if self._is_expired(cache_key):
            logger.info(f"Unit details cache entry expired for {plant_name}")
            self._remove_entry(cache_key)
            return None
        
        # Try memory cache first
        if cache_key in self.memory_cache:
            logger.info(f"Unit details retrieved from memory cache for {plant_name}")
            return self.memory_cache[cache_key]
        
        # Try disk cache
        cache_file = self._get_cache_file(cache_key)
        if cache_file.exists():
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    unit_details = json.load(f)
                
                # Store in memory cache for faster access
                self.memory_cache[cache_key] = unit_details
                
                logger.info(f"Unit details retrieved from disk cache for {plant_name}")
                return unit_details
                
            except Exception as e:
                logger.error(f"Failed to load cached unit details for {plant_name}: {e}")
                self._remove_entry(cache_key)
        
        return None
    
    def _remove_entry(self, cache_key: str):
        """Remove cache entry from memory, disk, and metadata."""
        # Remove from memory
        if cache_key in self.memory_cache:
            del self.memory_cache[cache_key]
        
        # Remove from disk
        cache_file = self._get_cache_file(cache_key)
        if cache_file.exists():
            try:
                cache_file.unlink()
            except Exception as e:
                logger.warning(f"Failed to remove cache file {cache_file}: {e}")
        
        # Remove from metadata
        if cache_key in self.metadata["entries"]:
            del self.metadata["entries"][cache_key]
            self._save_metadata()
    
    def cleanup_expired(self):
        """Remove all expired cache entries."""
        expired_keys = []
        
        for cache_key in list(self.metadata["entries"].keys()):
            if self._is_expired(cache_key):
                expired_keys.append(cache_key)
        
        for cache_key in expired_keys:
            self._remove_entry(cache_key)
        
        self.metadata["last_cleanup"] = datetime.now().isoformat()
        self._save_metadata()
        
        logger.info(f"Cleaned up {len(expired_keys)} expired cache entries")
    
    def get_cache_info(self) -> Dict[str, Any]:
        """Get information about current cache state."""
        total_entries = len(self.metadata["entries"])
        memory_entries = len(self.memory_cache)
        
        total_size = sum(
            entry.get("size_bytes", 0) 
            for entry in self.metadata["entries"].values()
        )
        
        return {
            "total_entries": total_entries,
            "memory_entries": memory_entries,
            "total_size_bytes": total_size,
            "total_size_mb": round(total_size / (1024 * 1024), 2),
            "cache_dir": str(self.cache_dir),
            "ttl_hours": self.cache_ttl.total_seconds() / 3600,
            "last_cleanup": self.metadata.get("last_cleanup"),
            "entries": list(self.metadata["entries"].keys())
        }
    
    def clear_all(self):
        """Clear all cache entries."""
        # Clear memory cache
        self.memory_cache.clear()
        
        # Remove all cache files
        for cache_file in self.cache_dir.glob("*.json"):
            if cache_file.name != "cache_metadata.json":
                try:
                    cache_file.unlink()
                except Exception as e:
                    logger.warning(f"Failed to remove cache file {cache_file}: {e}")
        
        # Reset metadata
        self.metadata = {
            "entries": {},
            "created": datetime.now().isoformat(),
            "last_cleanup": datetime.now().isoformat()
        }
        self._save_metadata()
        
        logger.info("All cache entries cleared")


# Global cache instance
plant_cache = PlantDataCache()
