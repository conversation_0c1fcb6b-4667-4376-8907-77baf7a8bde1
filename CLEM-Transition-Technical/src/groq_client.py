"""
Groq LLM client for power plant data extraction.
"""
import asyncio
import logging
from typing import Dict, List, Optional, Union, Any
import json
import re
from groq import AsyncGroq
from tenacity import retry, stop_after_attempt, wait_exponential

from src.models import ScrapedContent, ExtractionResult
from src.config import config

logger = logging.getLogger(__name__)


class GroqExtractionClient:
    """Client for extracting structured data using Groq LLM."""

    def __init__(self, api_key: str):
        # Use sync client only to avoid version compatibility issues
        from groq import Groq
        self.client = Groq(api_key=api_key)
        self.model = "llama-3.3-70b-versatile"
        self.is_async = False

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def extract_field(
        self,
        field_name: str,
        content: str,
        plant_name: str,
        organization_name: str = ""
    ) -> ExtractionResult:
        """
        Extract a specific field from content using LLM.

        Args:
            field_name: Name of the field to extract
            content: Text content to extract from
            plant_name: Name of the power plant
            organization_name: Organization name if known

        Returns:
            ExtractionResult with extracted value and confidence
        """
        # Get field-specific prompt
        prompt_template = config.extraction_prompts.get(field_name)
        if not prompt_template:
            raise ValueError(f"No prompt template found for field: {field_name}")

        # Format prompt with context - Reduce content length to save tokens
        content_truncated = content[:4000]
        if len(content) > 4000:
            logger.debug(f"Content truncated for {field_name}: {len(content)} → 4000 chars")

        context_vars = {
            "plant_name": plant_name,
            "organization_name": organization_name or plant_name,
            "content": content_truncated
        }

        prompt = prompt_template.format(**context_vars)

        try:
            # Use sync client in executor to avoid async issues
            response = await asyncio.get_event_loop().run_in_executor(
                None,
                lambda: self.client.chat.completions.create(
                    model=self.model,
                    messages=[
                        {
                            "role": "system",
                            "content": "You are an expert at extracting structured information about power plants from web content. Always return concise, accurate answers based only on the provided content."
                        },
                        {
                            "role": "user",
                            "content": prompt
                        }
                    ],
                    temperature=0.1,
                    max_tokens=100  # Reduced from 200 to 100 to save tokens
                )
            )

            raw_response = response.choices[0].message.content.strip()

            # Process and validate response
            extracted_value, confidence = self._process_response(field_name, raw_response)

            return ExtractionResult(
                field_name=field_name,
                extracted_value=extracted_value,
                confidence_score=confidence,
                source_url="aggregated",
                extraction_method="groq_llm",
                source_type="combined"
            )

        except Exception as e:
            logger.error(f"Groq extraction failed for field {field_name}: {e}")
            return ExtractionResult(
                field_name=field_name,
                extracted_value=None,
                confidence_score=0.0,
                source_url="error",
                extraction_method="groq_llm",
                source_type="error"
            )

    def _process_response(self, field_name: str, raw_response: str) -> tuple:
        """Process and validate LLM response."""
        response_lower = raw_response.lower().strip()

        # Handle "unknown" responses
        if any(term in response_lower for term in ["unknown", "unclear", "not found", "not mentioned"]):
            return None, 0.1

        # Field-specific processing
        if field_name == "cfpp_type":
            return self._process_plant_type(raw_response)
        elif field_name == "organization_name":
            return self._process_organization_name(raw_response)
        elif field_name == "country_name":
            return self._process_country_name(raw_response)
        elif field_name == "province":
            return self._process_province(raw_response)
        elif field_name == "plants_count":
            return self._process_plants_count(raw_response)
        elif field_name == "plant_types":
            return self._process_plant_types(raw_response)
        elif field_name == "ppa_flag":
            return self._process_ppa_flag(raw_response)
        elif field_name == "currency_in":
            return self._process_currency(raw_response)
        elif field_name == "financial_year":
            return self._process_financial_year(raw_response)
        else:
            return raw_response.strip(), 0.5

    def _process_plant_type(self, response: str) -> tuple:
        """Process ownership type response (cfpp_type now refers to ownership)."""
        response_lower = response.lower().strip()

        # Known ownership types mapping
        ownership_mapping = {
            "private": ["private", "privately owned", "private company", "private sector", "investor owned"],
            "public": ["public", "publicly owned", "government owned", "state owned", "state-owned",
                      "municipal", "government", "public sector", "nationalized"],
            "cooperative": ["cooperative", "co-op", "member owned", "community owned"],
            "joint_venture": ["joint venture", "joint-venture", "partnership", "public-private partnership",
                            "ppp", "consortium"]
        }

        for ownership_type, keywords in ownership_mapping.items():
            if any(keyword in response_lower for keyword in keywords):
                return ownership_type, 0.8

        # Check for exact matches to our expected values
        valid_types = ["private", "public", "cooperative", "joint_venture", "unknown"]
        if response_lower in valid_types:
            return response_lower, 0.9

        # Return original if no mapping found but seems valid
        if len(response.strip()) > 0 and len(response.strip()) < 50:
            return response.strip(), 0.6

        return "unknown", 0.1

    def _process_organization_name(self, response: str) -> tuple:
        """Process organization name response."""
        # Clean up common artifacts
        cleaned = response.strip()

        # Remove quotes
        cleaned = cleaned.strip('"\'')

        # Remove common prefixes/suffixes
        prefixes_to_remove = ["company:", "organization:", "owner:", "operator:"]
        for prefix in prefixes_to_remove:
            if cleaned.lower().startswith(prefix):
                cleaned = cleaned[len(prefix):].strip()

        # Validate length and content
        if 3 <= len(cleaned) <= 100 and not cleaned.lower() in ["unknown", "unclear"]:
            return cleaned, 0.8

        return None, 0.1

    def _process_country_name(self, response: str) -> tuple:
        """Process country name response."""
        cleaned = response.strip().strip('"\'')

        # Common country name mappings
        country_mapping = {
            "usa": "United States",
            "us": "United States",
            "america": "United States",
            "uk": "United Kingdom",
            "britain": "United Kingdom"
        }

        cleaned_lower = cleaned.lower()
        if cleaned_lower in country_mapping:
            return country_mapping[cleaned_lower], 0.9

        # Validate as reasonable country name
        if 3 <= len(cleaned) <= 50 and cleaned.replace(" ", "").isalpha():
            return cleaned.title(), 0.8

        return None, 0.1

    def _process_province(self, response: str) -> tuple:
        """Process province/state response."""
        cleaned = response.strip().strip('"\'')

        # Validate as reasonable province/state name
        if 2 <= len(cleaned) <= 50:
            return cleaned, 0.7

        return None, 0.1

    def _process_plants_count(self, response: str) -> tuple:
        """Process plants count response."""
        # Extract number from response
        numbers = re.findall(r'\d+', response)

        if numbers:
            try:
                count = int(numbers[0])
                if 1 <= count <= 1000:  # Reasonable range
                    return count, 0.8
            except ValueError:
                pass

        return None, 0.1

    def _process_plant_types(self, response: str) -> tuple:
        """Process plant types list response."""
        # Split by common delimiters
        types_raw = re.split(r'[,;|\n]', response.lower())

        valid_types = []
        known_types = ["coal", "gas", "nuclear", "solar", "wind", "hydro", "biomass", "geothermal", "oil"]

        for type_raw in types_raw:
            type_clean = type_raw.strip()
            if type_clean in known_types:
                valid_types.append(type_clean)

        if valid_types:
            return valid_types, 0.8

        return [], 0.1

    def _process_ppa_flag(self, response: str) -> tuple:
        """Process PPA flag response."""
        response_lower = response.lower().strip()

        # Check for explicit no PPA first (before general PPA terms)
        if any(term in response_lower for term in ["no ppa", "no agreement", "no contract", "no power purchase"]):
            return "unknown", 0.7

        # Check for Plant-level PPA
        elif any(term in response_lower for term in ["plant", "site-wide", "facility", "entire plant"]):
            return "Plant", 0.8
        # Check for Unit-level PPA
        elif any(term in response_lower for term in ["unit", "individual", "generating unit", "specific unit"]):
            return "Unit", 0.8
        # Check for general PPA existence (default to Plant level)
        elif any(term in response_lower for term in ["ppa", "power purchase", "offtake", "long-term contract"]):
            # Check if it's just mentioning "facility" without "no"
            if "facility" in response_lower and "no" not in response_lower:
                return "Plant", 0.8
            else:
                return "Plant", 0.6  # Lower confidence since level not specified

        return "unknown", 0.1

    def _process_currency(self, response: str) -> tuple:
        """Process currency code response."""
        # Extract 3-letter currency codes
        currency_match = re.search(r'\b[A-Z]{3}\b', response.upper())

        if currency_match:
            currency = currency_match.group()
            # Validate against common currencies
            common_currencies = ["USD", "EUR", "GBP", "JPY", "CNY", "INR", "CAD", "AUD", "BRL", "MXN"]
            if currency in common_currencies:
                return currency, 0.9
            else:
                return currency, 0.6

        return None, 0.1

    def _process_financial_year(self, response: str) -> tuple:
        """Process financial year response to MM-MM format."""
        response_clean = response.strip().lower()

        # Check if already in MM-MM format
        mm_mm_pattern = r'\b((0[1-9]|1[0-2])-(0[1-9]|1[0-2]))\b'
        mm_mm_match = re.search(mm_mm_pattern, response)
        if mm_mm_match:
            return mm_mm_match.group(1), 0.9

        # Map common fiscal year endings to MM-MM format
        fiscal_year_mappings = {
            # Ending month patterns
            'march': '04-03',
            'december': '01-12',
            'june': '07-06',
            'september': '10-09',
            'april': '05-04',
            'may': '06-05',
            'july': '08-07',
            'august': '09-08',
            'october': '11-10',
            'november': '12-11',
            'january': '02-01',
            'february': '03-02',

            # Handle single month responses (common LLM output)
            '01': '01-12',  # January start -> Calendar year
            '02': '02-01',  # February start
            '03': '03-02',  # March start
            '04': '04-03',  # April start -> April to March
            '05': '05-04',  # May start
            '06': '06-05',  # June start
            '07': '07-06',  # July start -> July to June (Australia)
            '08': '08-07',  # August start
            '09': '09-08',  # September start
            '10': '10-09',  # October start -> October to September
            '11': '11-10',  # November start
            '12': '12-11',  # December start

            # Common patterns
            'april to march': '04-03',
            'january to december': '01-12',
            'july to june': '07-06',
            'october to september': '10-09',
            'calendar year': '01-12',
            'financial year ending march': '04-03',
            'fiscal year ending december': '01-12',
            'fy ending march': '04-03',
            'year ending december': '01-12'
        }

        # Check for mapped patterns
        for pattern, mm_mm_format in fiscal_year_mappings.items():
            if pattern in response_clean:
                return mm_mm_format, 0.8

        # Check if response is exactly a month number (common LLM response)
        response_stripped = response.strip()
        if response_stripped in fiscal_year_mappings:
            return fiscal_year_mappings[response_stripped], 0.8

        # Default patterns by common mentions
        if any(term in response_clean for term in ['india', 'indian', 'uk', 'britain', 'japan']):
            return '04-03', 0.6  # April to March
        elif any(term in response_clean for term in ['usa', 'america', 'germany', 'europe']):
            return '01-12', 0.6  # January to December
        elif any(term in response_clean for term in ['australia', 'australian']):
            return '07-06', 0.6  # July to June

        return None, 0.1


class PowerPlantDataExtractor:
    """Orchestrates data extraction from scraped content using Groq LLM."""

    def __init__(self, groq_client: GroqExtractionClient):
        self.groq_client = groq_client

    async def extract_all_fields(
        self,
        scraped_contents: List[ScrapedContent],
        plant_name: str
    ) -> Dict[str, Any]:
        """
        Extract all organizational fields from scraped content.

        Args:
            scraped_contents: List of scraped web content
            plant_name: Name of the power plant

        Returns:
            Dictionary with extracted field values
        """
        logger.info(f"Starting LLM extraction for {len(scraped_contents)} content pieces")

        # Combine and prioritize content
        combined_content = self._combine_content(scraped_contents)

        # Fields to extract
        fields_to_extract = [
            "cfpp_type", "organization_name", "country_name", "province",
            "plants_count", "plant_types", "ppa_flag", "currency_in", "financial_year"
        ]

        extracted_data = {}
        organization_name = ""

        # Extract organization name first (needed for other extractions)
        org_result = await self.groq_client.extract_field(
            "organization_name", combined_content, plant_name
        )

        if org_result.confidence_score >= config.pipeline.confidence_threshold:
            organization_name = org_result.extracted_value or ""
            extracted_data["organization_name"] = organization_name
        else:
            extracted_data["organization_name"] = ""

        # Extract other fields
        for field_name in fields_to_extract:
            if field_name == "organization_name":
                continue  # Already extracted

            try:
                result = await self.groq_client.extract_field(
                    field_name, combined_content, plant_name, organization_name
                )

                if result.confidence_score >= config.pipeline.confidence_threshold:
                    extracted_data[field_name] = result.extracted_value
                else:
                    extracted_data[field_name] = self._get_default_value(field_name)

                logger.info(f"Extracted {field_name}: {result.extracted_value} (confidence: {result.confidence_score:.2f})")

                # Small delay between extractions
                await asyncio.sleep(0.5)

            except Exception as e:
                logger.error(f"Failed to extract {field_name}: {e}")
                extracted_data[field_name] = self._get_default_value(field_name)

        logger.info("LLM extraction completed")
        return extracted_data

    def _combine_content(self, scraped_contents: List[ScrapedContent]) -> str:
        """Combine scraped content with prioritization."""
        # Sort by relevance and source priority
        sorted_contents = sorted(
            scraped_contents,
            key=lambda x: (
                config.url_priority_weights.get(x.source_type, 0),
                x.relevance_score
            ),
            reverse=True
        )

        combined_text = ""
        total_length = 0
        max_combined_length = 8000  # Reduced from 15000 to 8000 to save tokens

        for content in sorted_contents:
            if total_length + len(content.content) > max_combined_length:
                # Add partial content if space allows
                remaining_space = max_combined_length - total_length
                if remaining_space > 500:  # Only if meaningful space left
                    combined_text += f"\n\n--- Source: {content.url} ---\n"
                    combined_text += content.content[:remaining_space]
                break

            combined_text += f"\n\n--- Source: {content.url} ---\n"
            combined_text += content.content
            total_length += len(content.content)

        return combined_text

    def _get_default_value(self, field_name: str) -> Any:
        """Get default value for field when extraction fails."""
        defaults = {
            "cfpp_type": "",
            "country_name": "",
            "currency_in": "",
            "financial_year": "",
            "organization_name": "",
            "plants_count": None,
            "plant_types": [],
            "ppa_flag": "",
            "province": ""
        }
        return defaults.get(field_name, "")
