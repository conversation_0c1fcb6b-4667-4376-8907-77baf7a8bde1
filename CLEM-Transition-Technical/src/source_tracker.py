"""
Source Tracker for Power Plant Data Extraction
Tracks and manages data sources for all extracted information.
"""
import json
import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class SourceTracker:
    """Tracks data sources for extracted information."""
    
    def __init__(self, sources_dir: str = "sources"):
        """Initialize the source tracker."""
        self.sources_dir = Path(sources_dir)
        self.sources_dir.mkdir(exist_ok=True)
        
        # Initialize source tracking data
        self.sources = {
            "extraction_session": {
                "session_id": datetime.now().strftime("%Y%m%d_%H%M%S"),
                "start_time": datetime.now().isoformat(),
                "plant_name": "",
                "unit_number": None
            },
            "cached_data_sources": {},
            "web_search_sources": [],
            "scraped_content_sources": [],
            "llm_processing_sources": [],
            "calculation_sources": [],
            "industry_standard_sources": []
        }
    
    def set_extraction_context(self, plant_name: str, unit_number: Optional[int] = None):
        """Set the context for current extraction."""
        self.sources["extraction_session"]["plant_name"] = plant_name
        self.sources["extraction_session"]["unit_number"] = unit_number
    
    def track_cached_source(
        self, 
        field_name: str, 
        cached_value: Any, 
        cache_timestamp: str,
        original_sources: List[str]
    ):
        """Track a field sourced from cache."""
        self.sources["cached_data_sources"][field_name] = {
            "value": str(cached_value),
            "cache_timestamp": cache_timestamp,
            "source_type": "cache",
            "original_extraction_sources": original_sources,
            "reliability": "high"  # Cached data is pre-validated
        }
    
    def track_web_search(
        self, 
        field_name: str, 
        search_query: str, 
        search_results: List[Dict[str, Any]],
        selected_result: Optional[Dict[str, Any]] = None
    ):
        """Track a web search performed for a field."""
        search_record = {
            "field_name": field_name,
            "search_query": search_query,
            "search_timestamp": datetime.now().isoformat(),
            "results_count": len(search_results),
            "search_results": search_results[:5],  # Store top 5 results
            "selected_result": selected_result,
            "search_engine": "Google via SERP API"
        }
        self.sources["web_search_sources"].append(search_record)
    
    def track_scraped_content(
        self, 
        field_name: str, 
        url: str, 
        content_snippet: str,
        extracted_value: Any,
        confidence_score: float = 0.0
    ):
        """Track content scraped from a specific URL."""
        scraped_record = {
            "field_name": field_name,
            "source_url": url,
            "scrape_timestamp": datetime.now().isoformat(),
            "content_snippet": content_snippet[:500],  # First 500 chars
            "extracted_value": str(extracted_value),
            "confidence_score": confidence_score,
            "scraping_method": "ScraperAPI"
        }
        self.sources["scraped_content_sources"].append(scraped_record)
    
    def track_llm_processing(
        self, 
        field_name: str, 
        input_content: str,
        llm_response: str,
        extracted_value: Any,
        llm_model: str = "claude-3-sonnet"
    ):
        """Track LLM processing for field extraction."""
        llm_record = {
            "field_name": field_name,
            "processing_timestamp": datetime.now().isoformat(),
            "input_content_length": len(input_content),
            "input_snippet": input_content[:200],
            "llm_model": llm_model,
            "llm_response_snippet": llm_response[:200],
            "extracted_value": str(extracted_value),
            "processing_method": "structured_extraction"
        }
        self.sources["llm_processing_sources"].append(llm_record)
    
    def track_calculation(
        self, 
        field_name: str, 
        calculation_method: str,
        input_values: Dict[str, Any],
        calculated_value: Any
    ):
        """Track calculated/derived values."""
        calc_record = {
            "field_name": field_name,
            "calculation_timestamp": datetime.now().isoformat(),
            "calculation_method": calculation_method,
            "input_values": input_values,
            "calculated_value": str(calculated_value),
            "calculation_type": "derived_from_plant_data"
        }
        self.sources["calculation_sources"].append(calc_record)
    
    def track_industry_standard(
        self, 
        field_name: str, 
        standard_value: Any,
        reference_source: str,
        country: str = "India"
    ):
        """Track values from industry standards."""
        standard_record = {
            "field_name": field_name,
            "standard_value": str(standard_value),
            "reference_source": reference_source,
            "country": country,
            "standard_type": "industry_benchmark",
            "applied_timestamp": datetime.now().isoformat()
        }
        self.sources["industry_standard_sources"].append(standard_record)
    
    def get_field_source_summary(self, field_name: str) -> Dict[str, Any]:
        """Get source summary for a specific field."""
        summary = {
            "field_name": field_name,
            "source_types": [],
            "primary_source": None,
            "reliability_score": 0.0,
            "source_details": []
        }
        
        # Check cached sources
        if field_name in self.sources["cached_data_sources"]:
            cached_info = self.sources["cached_data_sources"][field_name]
            summary["source_types"].append("cache")
            summary["primary_source"] = "cached_plant_data"
            summary["reliability_score"] = 0.9
            summary["source_details"].append({
                "type": "cache",
                "value": cached_info["value"],
                "timestamp": cached_info["cache_timestamp"]
            })
        
        # Check web search sources
        web_searches = [s for s in self.sources["web_search_sources"] if s["field_name"] == field_name]
        if web_searches:
            summary["source_types"].append("web_search")
            if not summary["primary_source"]:
                summary["primary_source"] = "web_search"
                summary["reliability_score"] = 0.7
            for search in web_searches:
                summary["source_details"].append({
                    "type": "web_search",
                    "query": search["search_query"],
                    "results_count": search["results_count"]
                })
        
        # Check scraped content sources
        scraped_content = [s for s in self.sources["scraped_content_sources"] if s["field_name"] == field_name]
        if scraped_content:
            summary["source_types"].append("scraped_content")
            if not summary["primary_source"]:
                summary["primary_source"] = "scraped_content"
                summary["reliability_score"] = 0.8
            for content in scraped_content:
                summary["source_details"].append({
                    "type": "scraped_content",
                    "url": content["source_url"],
                    "confidence": content["confidence_score"]
                })
        
        return summary
    
    def get_all_sources_summary(self) -> Dict[str, Any]:
        """Get comprehensive summary of all sources."""
        return {
            "extraction_session": self.sources["extraction_session"],
            "source_statistics": {
                "cached_fields": len(self.sources["cached_data_sources"]),
                "web_searches": len(self.sources["web_search_sources"]),
                "scraped_pages": len(self.sources["scraped_content_sources"]),
                "llm_extractions": len(self.sources["llm_processing_sources"]),
                "calculations": len(self.sources["calculation_sources"]),
                "industry_standards": len(self.sources["industry_standard_sources"])
            },
            "detailed_sources": self.sources
        }
    
    def save_sources_to_file(self, filename: Optional[str] = None) -> str:
        """Save source tracking data to file."""
        if not filename:
            session_id = self.sources["extraction_session"]["session_id"]
            plant_name = self.sources["extraction_session"]["plant_name"].lower().replace(" ", "_")
            unit_num = self.sources["extraction_session"]["unit_number"]
            if unit_num:
                filename = f"sources_{plant_name}_unit_{unit_num}_{session_id}.json"
            else:
                filename = f"sources_{plant_name}_{session_id}.json"
        
        filepath = self.sources_dir / filename
        
        # Add completion timestamp
        self.sources["extraction_session"]["completion_time"] = datetime.now().isoformat()
        
        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(self.sources, f, indent=2, ensure_ascii=False)
            
            logger.info(f"Source tracking data saved to: {filepath}")
            return str(filepath)
            
        except Exception as e:
            logger.error(f"Failed to save source tracking data: {e}")
            raise
    
    def load_sources_from_file(self, filepath: str):
        """Load source tracking data from file."""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                self.sources = json.load(f)
            
            logger.info(f"Source tracking data loaded from: {filepath}")
            
        except Exception as e:
            logger.error(f"Failed to load source tracking data: {e}")
            raise
    
    def generate_source_report(self) -> str:
        """Generate a human-readable source report."""
        session = self.sources["extraction_session"]
        stats = {
            "cached_fields": len(self.sources["cached_data_sources"]),
            "web_searches": len(self.sources["web_search_sources"]),
            "scraped_pages": len(self.sources["scraped_content_sources"]),
            "llm_extractions": len(self.sources["llm_processing_sources"]),
            "calculations": len(self.sources["calculation_sources"]),
            "industry_standards": len(self.sources["industry_standard_sources"])
        }
        
        report = f"""
DATA SOURCE REPORT
==================
Plant: {session['plant_name']}
Unit: {session.get('unit_number', 'N/A')}
Session: {session['session_id']}
Extraction Time: {session['start_time']}

SOURCE STATISTICS
-----------------
• Cached fields: {stats['cached_fields']}
• Web searches performed: {stats['web_searches']}
• Pages scraped: {stats['scraped_pages']}
• LLM extractions: {stats['llm_extractions']}
• Calculated values: {stats['calculations']}
• Industry standards applied: {stats['industry_standards']}

CACHED DATA SOURCES
-------------------
"""
        
        for field, info in self.sources["cached_data_sources"].items():
            report += f"• {field}: {info['value']} (cached from {info['cache_timestamp']})\n"
        
        report += "\nWEB SEARCH QUERIES\n------------------\n"
        for search in self.sources["web_search_sources"]:
            report += f"• {search['field_name']}: {search['search_query']}\n"
        
        report += "\nCALCULATED VALUES\n-----------------\n"
        for calc in self.sources["calculation_sources"]:
            report += f"• {calc['field_name']}: {calc['calculation_method']}\n"
        
        return report


# Global source tracker instance
source_tracker = SourceTracker()
