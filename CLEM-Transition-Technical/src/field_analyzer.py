"""
Intelligent field analyzer for determining what plant details can be extracted from existing data
and what requires additional searches.
"""
import logging
import json
import re
from typing import Dict, List, Set, Any, Optional, Tuple
from datetime import datetime

from src.models import ScrapedContent, OrganizationalDetails, PlantDetails
from src.config import config

logger = logging.getLogger(__name__)


class PlantFieldAnalyzer:
    """Analyzes scraped content to determine which plant details fields can be extracted."""

    def __init__(self):
        # Define field extraction patterns and keywords
        self.field_patterns = {
            "name": {
                "keywords": ["power plant", "nuclear plant", "generating station", "facility", "power station"],
                "confidence_threshold": 0.7
            },
            "plant_type": {
                "keywords": ["technology", "fuel type", "nuclear", "coal", "gas", "solar", "wind", "hydro", "biomass", "geothermal"],
                "confidence_threshold": 0.8
            },
            "plant_address": {
                "keywords": ["district", "city", "state", "country", "located at", "situated in", "address"],
                "confidence_threshold": 0.6
            },
            "lat": {
                "keywords": ["plant's own latitude", "latitude coordinate", "decimal degrees", "GPS", "°N", "°S"],
                "confidence_threshold": 0.9
            },
            "long": {
                "keywords": ["plant's own longitude", "longitude coordinate", "decimal degrees", "GPS", "°E", "°W"],
                "confidence_threshold": 0.9
            },
            "units_id": {
                "keywords": ["integers from 1", "number of units", "unit 1", "unit 2", "turbine", "generator", "reactor", "block"],
                "confidence_threshold": 0.7
            },
            "grid_connectivity_maps": {
                "keywords": ["rated capacity", "substation", "transmission", "grid", "interconnection", "tie-in", "electrical", "classification", "voltage level"],
                "confidence_threshold": 0.6
            },
            "ppa_details": {
                "keywords": ["capacity covered", "PPA commencement", "termination date", "entity procuring power", "power purchase agreement", "PPA", "offtake", "contract"],
                "confidence_threshold": 0.6
            }
        }

    def analyze_existing_content(
        self,
        scraped_contents: List[ScrapedContent],
        org_details: Optional[OrganizationalDetails] = None
    ) -> Dict[str, Any]:
        """
        Analyze existing scraped content to determine which plant fields can be extracted.

        Args:
            scraped_contents: Previously scraped content
            org_details: Organizational details if available

        Returns:
            Analysis results with extractable fields and missing fields
        """
        logger.info(f"Analyzing {len(scraped_contents)} scraped contents for plant details extraction")

        analysis = {
            "extractable_fields": {},
            "missing_fields": [],
            "content_coverage": {},
            "recommended_searches": [],
            "total_content_chars": sum(len(content.content) for content in scraped_contents)
        }

        # Analyze each field
        for field_name, field_config in self.field_patterns.items():
            field_analysis = self._analyze_field_extractability(
                field_name, field_config, scraped_contents, org_details
            )

            if field_analysis["extractable"]:
                analysis["extractable_fields"][field_name] = field_analysis
            else:
                analysis["missing_fields"].append(field_name)
                analysis["recommended_searches"].extend(field_analysis.get("recommended_searches", []))

        # Calculate content coverage by source type
        analysis["content_coverage"] = self._calculate_content_coverage(scraped_contents)

        logger.info(f"Field analysis complete: {len(analysis['extractable_fields'])} extractable, {len(analysis['missing_fields'])} missing")

        return analysis

    def _analyze_field_extractability(
        self,
        field_name: str,
        field_config: Dict,
        scraped_contents: List[ScrapedContent],
        org_details: Optional[OrganizationalDetails]
    ) -> Dict[str, Any]:
        """Analyze if a specific field can be extracted from existing content."""

        field_analysis = {
            "extractable": False,
            "confidence": 0.0,
            "source_count": 0,
            "best_sources": [],
            "recommended_searches": []
        }

        # Check for field-specific content in scraped data
        relevant_sources = []
        total_keyword_matches = 0

        for content in scraped_contents:
            keyword_matches = self._count_keyword_matches(content.content, field_config["keywords"])
            if keyword_matches > 0:
                relevant_sources.append({
                    "url": content.url,
                    "source_type": content.source_type,
                    "keyword_matches": keyword_matches,
                    "relevance_score": content.relevance_score,
                    "content_length": len(content.content)
                })
                total_keyword_matches += keyword_matches

        # Sort sources by relevance
        relevant_sources.sort(key=lambda x: (x["keyword_matches"], x["relevance_score"]), reverse=True)

        # Determine extractability
        if relevant_sources:
            # Calculate confidence based on keyword matches and source quality
            base_confidence = min(total_keyword_matches / 10.0, 1.0)  # Normalize to 0-1
            source_quality_bonus = sum(s["relevance_score"] for s in relevant_sources[:3]) / 3.0
            field_analysis["confidence"] = min((base_confidence + source_quality_bonus) / 2.0, 1.0)

            field_analysis["extractable"] = field_analysis["confidence"] >= field_config["confidence_threshold"]
            field_analysis["source_count"] = len(relevant_sources)
            field_analysis["best_sources"] = relevant_sources[:3]

        # Add field-specific logic
        field_analysis = self._add_field_specific_logic(field_name, field_analysis, org_details)

        # Generate recommended searches if not extractable
        if not field_analysis["extractable"]:
            field_analysis["recommended_searches"] = self._generate_field_searches(field_name)

        return field_analysis

    def _add_field_specific_logic(
        self,
        field_name: str,
        field_analysis: Dict,
        org_details: Optional[OrganizationalDetails]
    ) -> Dict[str, Any]:
        """Add field-specific extraction logic."""

        # Use organizational data to boost plant field extraction
        if org_details:
            if field_name == "name" and org_details.organization_name:
                # If we have org name, plant name extraction is more likely
                field_analysis["confidence"] = min(field_analysis["confidence"] + 0.2, 1.0)

            elif field_name == "plant_type" and org_details.plant_types:
                # If we have plant types from org data, boost confidence
                field_analysis["confidence"] = min(field_analysis["confidence"] + 0.3, 1.0)
                field_analysis["extractable"] = True

            elif field_name in ["lat", "long"] and org_details.province and org_details.country_name:
                # If we have location info, coordinates might be findable
                field_analysis["confidence"] = min(field_analysis["confidence"] + 0.1, 1.0)

        # Field-specific patterns
        if field_name in ["lat", "long"]:
            # Coordinates require very specific patterns
            field_analysis["confidence"] *= 0.8  # Be more conservative

        return field_analysis

    def _count_keyword_matches(self, content: str, keywords: List[str]) -> int:
        """Count keyword matches in content."""
        content_lower = content.lower()
        matches = 0

        for keyword in keywords:
            # Use word boundaries for better matching
            pattern = r'\b' + re.escape(keyword.lower()) + r'\b'
            matches += len(re.findall(pattern, content_lower))

        return matches

    def _calculate_content_coverage(self, scraped_contents: List[ScrapedContent]) -> Dict[str, Any]:
        """Calculate content coverage by source type."""
        coverage = {
            "by_source_type": {},
            "total_sources": len(scraped_contents),
            "avg_relevance": 0.0
        }

        if not scraped_contents:
            return coverage

        # Group by source type
        for content in scraped_contents:
            source_type = content.source_type
            if source_type not in coverage["by_source_type"]:
                coverage["by_source_type"][source_type] = {
                    "count": 0,
                    "total_chars": 0,
                    "avg_relevance": 0.0
                }

            coverage["by_source_type"][source_type]["count"] += 1
            coverage["by_source_type"][source_type]["total_chars"] += len(content.content)
            coverage["by_source_type"][source_type]["avg_relevance"] += content.relevance_score

        # Calculate averages
        for source_type, data in coverage["by_source_type"].items():
            data["avg_relevance"] /= data["count"]

        coverage["avg_relevance"] = sum(c.relevance_score for c in scraped_contents) / len(scraped_contents)

        return coverage

    def _generate_field_searches(self, field_name: str) -> List[str]:
        """Generate recommended search categories for missing fields."""

        search_mapping = {
            "lat": ["plant_coordinates"],
            "long": ["plant_coordinates"],
            "grid_connectivity_maps": ["grid_connectivity"],
            "ppa_details": ["ppa_contracts"],
            "units_id": ["plant_units"],
            "plant_address": ["plant_coordinates", "plant_specifications"],
            "name": ["basic_discovery"],
            "plant_type": ["technical_details", "plant_specifications"]
        }

        return search_mapping.get(field_name, ["plant_specifications"])

    def determine_search_strategy(self, analysis: Dict[str, Any], plant_name: str) -> Dict[str, Any]:
        """
        Determine the optimal search strategy based on field analysis.

        Args:
            analysis: Field analysis results
            plant_name: Name of the plant

        Returns:
            Search strategy with specific categories and priorities
        """
        strategy = {
            "skip_search": len(analysis["missing_fields"]) == 0,
            "search_categories": [],
            "priority_fields": analysis["missing_fields"],
            "estimated_api_calls": 0
        }

        if strategy["skip_search"]:
            logger.info("All plant details fields can be extracted from existing content - skipping additional searches")
            return strategy

        # Determine which search categories are needed
        needed_categories = set()
        for field in analysis["missing_fields"]:
            recommended = self._generate_field_searches(field)
            needed_categories.update(recommended)

        strategy["search_categories"] = list(needed_categories)
        strategy["estimated_api_calls"] = len(needed_categories) * 3  # Rough estimate

        logger.info(f"Search strategy: {len(strategy['search_categories'])} categories needed for {len(analysis['missing_fields'])} missing fields")

        return strategy
