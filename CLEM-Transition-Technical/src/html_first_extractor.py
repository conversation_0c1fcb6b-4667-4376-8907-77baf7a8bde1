"""
HTML-first extraction strategy with targeted PDF processing.
"""
import asyncio
import logging
from typing import Dict, List, Optional, Tuple
from collections import defaultdict

from src.models import ScrapedContent, ExtractionResult, FieldExtractionSummary, OrganizationalDetails
from src.groq_client import GroqExtractionClient
from src.config import config

logger = logging.getLogger(__name__)


class HtmlFirstExtractor:
    """Extractor that processes HTML first, then uses PDFs to fill gaps."""

    def __init__(self, groq_api_key: str):
        self.groq_client = GroqExtractionClient(groq_api_key)

        # Define field-specific confidence thresholds
        self.confidence_thresholds = {
            'cfpp_type': 0.7,
            'organization_name': 0.8,
            'country_name': 0.7,
            'province': 0.6,
            'plants_count': 0.7,
            'plant_types': 0.6,
            'ppa_flag': 0.5,
            'currency_in': 0.6,
            'financial_year': 0.6
        }

        # Define which PDFs are best for which fields
        self.pdf_field_priorities = {
            'financial_year': ['annual-report', 'financial', '10-k', 'sustainability'],
            'currency_in': ['annual-report', 'financial', '10-k'],
            'plants_count': ['annual-report', 'sustainability', 'corporate'],
            'ppa_flag': ['annual-report', 'contract', 'agreement', 'regulatory'],
            'organization_name': ['annual-report', 'corporate', 'about'],
            'plant_types': ['annual-report', 'sustainability', 'technical']
        }

    async def extract_with_html_first_strategy(
        self,
        scraped_contents: List[ScrapedContent],
        plant_name: str
    ) -> OrganizationalDetails:
        """
        Main extraction method using HTML-first strategy.

        Args:
            scraped_contents: List of scraped content (HTML + PDF)
            plant_name: Name of the power plant

        Returns:
            OrganizationalDetails object with extracted data
        """
        logger.info(f"Starting HTML-first extraction for {plant_name}")

        # Phase 1: Separate content by type
        html_contents, pdf_contents = self._separate_content_by_type(scraped_contents)

        logger.info(f"Content separation: {len(html_contents)} HTML, {len(pdf_contents)} PDF sources")

        # Phase 2: Extract from HTML sources first
        html_results = await self._extract_from_html_sources(html_contents, plant_name)

        # Phase 3: Analyze gaps and determine PDF processing needs
        missing_fields = self._identify_missing_fields(html_results)

        if missing_fields:
            logger.info(f"Missing fields identified: {missing_fields}")
            # Phase 4: Targeted PDF processing for missing fields
            pdf_results = await self._extract_from_pdfs_targeted(
                pdf_contents, missing_fields, plant_name
            )
        else:
            logger.info("All fields extracted from HTML sources")
            pdf_results = {}

        # Phase 5: Merge results intelligently
        final_results = self._merge_html_pdf_results(html_results, pdf_results)

        # Phase 6: Convert to OrganizationalDetails
        org_details = self._create_organizational_details(final_results)

        logger.info(f"HTML-first extraction completed for {plant_name}")
        self._log_extraction_summary(final_results)

        return org_details

    def _separate_content_by_type(
        self,
        scraped_contents: List[ScrapedContent]
    ) -> Tuple[List[ScrapedContent], List[ScrapedContent]]:
        """Separate content into HTML and PDF sources."""
        html_contents = []
        pdf_contents = []

        for content in scraped_contents:
            # Check if it's a PDF based on URL or source indicators
            if (content.url.lower().endswith('.pdf') or
                '.pdf?' in content.url.lower() or
                'pdf' in content.url.lower().split('/')[-1]):
                pdf_contents.append(content)
            else:
                html_contents.append(content)

        return html_contents, pdf_contents

    async def _extract_from_html_sources(
        self,
        html_contents: List[ScrapedContent],
        plant_name: str
    ) -> Dict[str, FieldExtractionSummary]:
        """Extract all fields from HTML sources only."""
        logger.info(f"Extracting from {len(html_contents)} HTML sources")

        if not html_contents:
            logger.warning("No HTML content available for extraction")
            return {}

        # Combine HTML content
        combined_html = self._combine_content(html_contents)

        # Extract all fields from HTML
        html_results = {}
        fields_to_extract = list(self.confidence_thresholds.keys())

        for field_name in fields_to_extract:
            try:
                result = await self.groq_client.extract_field(
                    field_name, combined_html, plant_name
                )

                # Create field summary
                html_results[field_name] = FieldExtractionSummary(
                    field_name=field_name,
                    final_value=result.extracted_value,
                    confidence_score=result.confidence_score,
                    source_type="html",
                    source_url="combined_html",
                    extraction_method="groq_html",
                    is_complete=result.confidence_score >= self.confidence_thresholds[field_name]
                )

                logger.info(f"HTML extraction - {field_name}: {result.extracted_value} "
                          f"(confidence: {result.confidence_score:.2f})")

                # Rate limiting
                await asyncio.sleep(0.3)

            except Exception as e:
                logger.error(f"Failed to extract {field_name} from HTML: {e}")
                html_results[field_name] = FieldExtractionSummary(
                    field_name=field_name,
                    final_value=None,
                    confidence_score=0.0,
                    source_type="html",
                    is_complete=False
                )

        return html_results

    def _identify_missing_fields(
        self,
        html_results: Dict[str, FieldExtractionSummary]
    ) -> List[str]:
        """Identify fields that need PDF processing."""
        missing_fields = []

        for field_name, summary in html_results.items():
            if not summary.is_complete:
                missing_fields.append(field_name)
                logger.debug(f"Field {field_name} needs PDF processing "
                           f"(confidence: {summary.confidence_score:.2f})")

        return missing_fields

    async def _extract_from_pdfs_targeted(
        self,
        pdf_contents: List[ScrapedContent],
        missing_fields: List[str],
        plant_name: str
    ) -> Dict[str, FieldExtractionSummary]:
        """Extract only specific missing fields from PDFs."""
        logger.info(f"Targeted PDF extraction for fields: {missing_fields}")

        if not pdf_contents:
            logger.warning("No PDF content available for gap filling")
            return {}

        pdf_results = {}

        for field_name in missing_fields:
            try:
                # Find best PDF for this field
                best_pdf = self._find_best_pdf_for_field(pdf_contents, field_name)

                if best_pdf:
                    logger.info(f"Processing PDF for {field_name}: {best_pdf.url}")

                    # Extract only this specific field
                    result = await self.groq_client.extract_field(
                        field_name, best_pdf.content[:6000], plant_name  # Limit PDF content
                    )

                    pdf_results[field_name] = FieldExtractionSummary(
                        field_name=field_name,
                        final_value=result.extracted_value,
                        confidence_score=result.confidence_score,
                        source_type="pdf",
                        source_url=best_pdf.url,
                        extraction_method="groq_pdf_targeted",
                        is_complete=result.confidence_score >= self.confidence_thresholds[field_name]
                    )

                    logger.info(f"PDF extraction - {field_name}: {result.extracted_value} "
                              f"(confidence: {result.confidence_score:.2f})")

                    # Rate limiting
                    await asyncio.sleep(0.3)
                else:
                    logger.warning(f"No suitable PDF found for field: {field_name}")

            except Exception as e:
                logger.error(f"Failed to extract {field_name} from PDF: {e}")

        return pdf_results

    def _find_best_pdf_for_field(
        self,
        pdf_contents: List[ScrapedContent],
        field_name: str
    ) -> Optional[ScrapedContent]:
        """Find the best PDF for extracting a specific field."""
        if field_name not in self.pdf_field_priorities:
            # Return highest relevance PDF if no specific priority
            return max(pdf_contents, key=lambda x: x.relevance_score) if pdf_contents else None

        priority_keywords = self.pdf_field_priorities[field_name]

        # Score PDFs based on URL keywords and relevance
        scored_pdfs = []
        for pdf in pdf_contents:
            score = pdf.relevance_score

            # Boost score for relevant URL keywords
            url_lower = pdf.url.lower()
            for keyword in priority_keywords:
                if keyword in url_lower:
                    score += 0.3

            scored_pdfs.append((score, pdf))

        if scored_pdfs:
            # Return PDF with highest score
            return max(scored_pdfs, key=lambda x: x[0])[1]

        return None

    def _merge_html_pdf_results(
        self,
        html_results: Dict[str, FieldExtractionSummary],
        pdf_results: Dict[str, FieldExtractionSummary]
    ) -> Dict[str, FieldExtractionSummary]:
        """Merge HTML and PDF results intelligently."""
        final_results = html_results.copy()

        for field_name, pdf_summary in pdf_results.items():
            html_summary = html_results.get(field_name)

            if html_summary:
                # Use PDF result if significantly better or HTML is incomplete
                if (pdf_summary.confidence_score > html_summary.confidence_score + 0.2 or
                    not html_summary.is_complete):
                    final_results[field_name] = pdf_summary
                    logger.info(f"Using PDF result for {field_name} "
                              f"(PDF: {pdf_summary.confidence_score:.2f} vs "
                              f"HTML: {html_summary.confidence_score:.2f})")
            else:
                # No HTML result, use PDF
                final_results[field_name] = pdf_summary

        return final_results

    def _create_organizational_details(
        self,
        extraction_results: Dict[str, FieldExtractionSummary]
    ) -> OrganizationalDetails:
        """Convert extraction results to OrganizationalDetails object."""
        data = {}

        for field_name, summary in extraction_results.items():
            if summary.final_value is not None and summary.is_complete:
                data[field_name] = summary.final_value
            else:
                # Use default values for incomplete fields
                defaults = {
                    "cfpp_type": "",
                    "country_name": "",
                    "currency_in": "",
                    "financial_year": "",
                    "organization_name": "",
                    "plants_count": None,
                    "plant_types": [],
                    "ppa_flag": "",
                    "province": ""
                }
                data[field_name] = defaults.get(field_name, "")

        return OrganizationalDetails(**data)

    def _combine_content(self, contents: List[ScrapedContent]) -> str:
        """Combine content with prioritization."""
        # Sort by relevance and source priority
        sorted_contents = sorted(
            contents,
            key=lambda x: (
                config.url_priority_weights.get(x.source_type, 0),
                x.relevance_score
            ),
            reverse=True
        )

        combined_text = ""
        total_length = 0
        max_combined_length = 6000  # Smaller for HTML-only processing

        for content in sorted_contents:
            if total_length + len(content.content) > max_combined_length:
                # Add partial content if space allows
                remaining_space = max_combined_length - total_length
                if remaining_space > 500:
                    combined_text += f"\n\n--- Source: {content.url} ---\n"
                    combined_text += content.content[:remaining_space]
                break

            combined_text += f"\n\n--- Source: {content.url} ---\n"
            combined_text += content.content
            total_length += len(content.content)

        return combined_text

    def _log_extraction_summary(self, results: Dict[str, FieldExtractionSummary]):
        """Log summary of extraction results."""
        html_fields = [f for f, r in results.items() if r.source_type == "html" and r.is_complete]
        pdf_fields = [f for f, r in results.items() if r.source_type == "pdf" and r.is_complete]
        incomplete_fields = [f for f, r in results.items() if not r.is_complete]

        logger.info(f"Extraction Summary:")
        logger.info(f"  HTML-extracted fields: {len(html_fields)} - {html_fields}")
        logger.info(f"  PDF-extracted fields: {len(pdf_fields)} - {pdf_fields}")
        logger.info(f"  Incomplete fields: {len(incomplete_fields)} - {incomplete_fields}")

        total_complete = len(html_fields) + len(pdf_fields)
        logger.info(f"  Overall completion: {total_complete}/{len(results)} fields")
