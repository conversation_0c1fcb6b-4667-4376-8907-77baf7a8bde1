"""
Plant details extractor for technical power plant information with intelligent caching.
"""
import asyncio
import logging
import json
from typing import Dict, List, Optional, Any
from datetime import datetime

from src.models import ScrapedContent, ExtractionResult, PlantDetails, OrganizationalDetails
from src.groq_client import Groq<PERSON>xtraction<PERSON>lient
from src.openai_client import OpenAIExtractionClient
from src.field_analyzer import PlantFieldAnalyzer
from src.config import config

logger = logging.getLogger(__name__)


class PlantDetailsExtractor:
    """Extractor for plant technical details using LLM with intelligent caching."""

    def __init__(self, groq_client: GroqExtractionClient = None, use_bedrock: bool = False, use_openai: bool = False, openai_api_key: str = None):
        if use_openai and openai_api_key:
            # Use OpenAI as primary LLM
            self.llm_client = OpenAIExtractionClient(
                api_key=openai_api_key,
                model=config.pipeline.openai_model
            )
            logger.info("Plant details extractor initialized with OpenAI")
        elif use_bedrock:
            # Bedrock support removed - fallback to Groq
            logger.warning("Bedrock support has been removed. Falling back to Groq.")
            if groq_client:
                self.llm_client = groq_client
                logger.info("Plant details extractor initialized with Groq (fallback)")
            else:
                raise ValueError("Bedrock support removed and no Groq client provided")
        else:
            # Use Groq
            self.llm_client = groq_client
            logger.info("Plant details extractor initialized with Groq")

        # Keep groq_client for backward compatibility
        self.groq_client = self.llm_client
        self.field_analyzer = PlantFieldAnalyzer()

    async def extract_with_cache_optimization(
        self,
        scraped_contents: List[ScrapedContent],
        plant_name: str,
        org_details: Optional[OrganizationalDetails] = None
    ) -> Dict[str, Any]:
        """
        Extract plant details with intelligent cache optimization.

        Args:
            scraped_contents: Previously scraped content (from org extraction)
            plant_name: Name of the power plant
            org_details: Organizational details if available

        Returns:
            Dictionary with extracted plant details and cache analysis
        """
        logger.info(f"Starting cache-optimized plant details extraction for: {plant_name}")

        # Phase 1: Analyze existing content
        analysis = self.field_analyzer.analyze_existing_content(scraped_contents, org_details)

        logger.info(f"Cache analysis: {len(analysis['extractable_fields'])} fields extractable from cache, "
                   f"{len(analysis['missing_fields'])} fields need additional data")

        # Phase 2: Extract from cached content
        cached_extraction = await self._extract_from_cached_content(
            scraped_contents, plant_name, analysis["extractable_fields"], org_details
        )

        # Phase 3: Determine what's still missing
        missing_fields = self._identify_missing_fields(cached_extraction, analysis["missing_fields"])

        result = {
            "extracted_data": cached_extraction,
            "cache_analysis": analysis,
            "missing_fields": missing_fields,
            "extraction_source": "cache_optimized"
        }

        logger.info(f"Cache-optimized extraction completed. "
                   f"Extracted: {len([k for k, v in cached_extraction.items() if v not in [None, '', []]])} fields")

        return result

    async def _extract_from_cached_content(
        self,
        scraped_contents: List[ScrapedContent],
        plant_name: str,
        extractable_fields: Dict[str, Any],
        org_details: Optional[OrganizationalDetails] = None
    ) -> Dict[str, Any]:
        """Extract plant details from cached scraped content."""

        logger.info(f"Extracting {len(extractable_fields)} fields from cached content")

        # Combine content for extraction
        combined_content = self._combine_content(scraped_contents)

        extracted_data = {}

        # Extract fields that can be extracted from cache
        for field_name in extractable_fields.keys():
            try:
                # Use organizational data to enhance extraction
                enhanced_content = self._enhance_content_with_org_data(
                    combined_content, org_details, field_name
                )

                result = await self._extract_plant_field(field_name, enhanced_content, plant_name)

                if result.confidence_score >= config.pipeline.confidence_threshold:
                    extracted_data[field_name] = result.extracted_value
                else:
                    extracted_data[field_name] = self._get_default_plant_value(field_name)

                logger.info(f"Cached extraction - {field_name}: {result.extracted_value} "
                           f"(confidence: {result.confidence_score:.2f})")

                # Small delay between extractions
                await asyncio.sleep(0.3)

            except Exception as e:
                logger.error(f"Failed to extract {field_name} from cache: {e}")
                extracted_data[field_name] = self._get_default_plant_value(field_name)

        # Add fields that can be derived from organizational data
        extracted_data = self._derive_from_org_data(extracted_data, org_details, plant_name)

        return extracted_data

    def _enhance_content_with_org_data(
        self,
        content: str,
        org_details: Optional[OrganizationalDetails],
        field_name: str
    ) -> str:
        """Enhance content with organizational data for better extraction."""

        if not org_details:
            return content

        enhancement = []

        # Add relevant organizational context
        if org_details.organization_name:
            enhancement.append(f"Organization: {org_details.organization_name}")

        if org_details.country_name:
            enhancement.append(f"Country: {org_details.country_name}")

        if org_details.province:
            enhancement.append(f"Province/State: {org_details.province}")

        if org_details.plant_types and field_name == "plant_type":
            enhancement.append(f"Known plant types: {', '.join(org_details.plant_types)}")

        if enhancement:
            enhanced_content = "ORGANIZATIONAL CONTEXT:\n" + "\n".join(enhancement) + "\n\nCONTENT:\n" + content
            return enhanced_content

        return content

    def _derive_from_org_data(
        self,
        extracted_data: Dict[str, Any],
        org_details: Optional[OrganizationalDetails],
        plant_name: str
    ) -> Dict[str, Any]:
        """Derive plant details from organizational data where possible."""

        if not org_details:
            return extracted_data

        # Use organization name for plant name field
        if org_details.organization_name:
            extracted_data["name"] = org_details.organization_name
            logger.info(f"Using organization name for plant name: {extracted_data['name']}")
        elif not extracted_data.get("name") or extracted_data["name"] == "":
            # Fallback to input plant name if no organization name
            extracted_data["name"] = plant_name
            logger.info(f"Using input plant name as fallback: {extracted_data['name']}")

        # Derive plant type from organizational plant types
        if (not extracted_data.get("plant_type") or extracted_data["plant_type"] == "") and org_details.plant_types:
            # Use the first plant type as a reasonable guess
            extracted_data["plant_type"] = org_details.plant_types[0]
            logger.info(f"Derived plant_type from org data: {extracted_data['plant_type']}")

        # Generate plant_id if not present
        if not extracted_data.get("plant_id"):
            extracted_data["plant_id"] = self._generate_plant_id(plant_name)

        return extracted_data

    def _identify_missing_fields(self, extracted_data: Dict[str, Any], potential_missing: List[str]) -> List[str]:
        """Identify which fields are still missing after cache extraction."""

        missing = []

        for field in potential_missing:
            value = extracted_data.get(field)

            # Check if field is truly missing or has default/empty value
            if value is None or value == "" or (isinstance(value, list) and len(value) == 0):
                missing.append(field)

        return missing

    async def extract_all_plant_details(
        self,
        scraped_contents: List[ScrapedContent],
        plant_name: str,
        org_details: Optional[OrganizationalDetails] = None
    ) -> Dict[str, Any]:
        """
        Extract all plant technical details from scraped content.

        Args:
            scraped_contents: List of scraped web content
            plant_name: Name of the power plant

        Returns:
            Dictionary with extracted plant details
        """
        logger.info(f"Starting plant details extraction for: {plant_name}")

        if not scraped_contents:
            logger.warning("No scraped content available for plant details extraction")
            return self._get_empty_plant_details()

        # Combine all content for extraction
        combined_content = self._combine_content(scraped_contents)

        # Fields to extract for plant details
        plant_fields = [
            "name", "plant_type", "plant_address", "lat", "long",
            "units_id", "grid_connectivity", "ppa_details"
        ]

        extracted_data = {}

        # Extract each field
        for field_name in plant_fields:
            try:
                result = await self._extract_plant_field(
                    field_name, combined_content, plant_name
                )

                if result.confidence_score >= config.pipeline.confidence_threshold:
                    extracted_data[field_name] = result.extracted_value
                else:
                    extracted_data[field_name] = self._get_default_plant_value(field_name)

                logger.info(f"Extracted {field_name}: {result.extracted_value} (confidence: {result.confidence_score:.2f})")

                # Small delay between extractions
                await asyncio.sleep(0.5)

            except Exception as e:
                logger.error(f"Failed to extract {field_name}: {e}")
                extracted_data[field_name] = self._get_default_plant_value(field_name)

        # Generate plant_id if not provided
        if not extracted_data.get("plant_id"):
            extracted_data["plant_id"] = self._generate_plant_id(plant_name)

        # Process complex fields
        extracted_data = self._process_complex_fields(extracted_data)

        # Derive additional data from organizational details
        if org_details:
            extracted_data = self._derive_from_org_data(extracted_data, org_details, plant_name)

        logger.info(f"Plant details extraction completed for: {plant_name}")
        return extracted_data

    async def _extract_plant_field(
        self,
        field_name: str,
        content: str,
        plant_name: str
    ) -> ExtractionResult:
        """Extract a specific plant detail field."""

        # Get field-specific prompt
        prompt_template = config.plant_details_extraction_prompts.get(field_name)
        if not prompt_template:
            raise ValueError(f"No prompt template found for plant field: {field_name}")

        # Format prompt with content and plant name
        formatted_prompt = prompt_template.format(
            content=content[:8000],  # Limit content length
            plant_name=plant_name
        )

        try:
            # Use LLM client for extraction (OpenAI or Groq)
            if hasattr(self.llm_client, 'extract_field') and hasattr(self.llm_client, '_extract_with_prompt'):
                # Use OpenAI client
                raw_response = await self.llm_client.extract_field(formatted_prompt)
                extraction_method = "openai_llm_plant_details"
            else:
                # Use Groq client
                response = self.llm_client.client.chat.completions.create(
                    model=self.llm_client.model,
                    messages=[
                        {
                            "role": "system",
                            "content": "You are an expert at extracting technical information about power plants from documents. Be precise and only extract information that is explicitly stated."
                        },
                        {
                            "role": "user",
                            "content": formatted_prompt
                        }
                    ],
                    temperature=0.1,
                    max_tokens=1000
                )
                raw_response = response.choices[0].message.content.strip()
                extraction_method = "groq_llm_plant_details"

            if not raw_response:
                raise ValueError("Empty response from LLM")

            # Process and validate response
            extracted_value, confidence = self._process_plant_response(field_name, raw_response)

            return ExtractionResult(
                field_name=field_name,
                extracted_value=extracted_value,
                confidence_score=confidence,
                source_url="aggregated",
                extraction_method=extraction_method,
                source_type="combined"
            )

        except Exception as e:
            logger.error(f"LLM extraction failed for plant field {field_name}: {e}")
            return ExtractionResult(
                field_name=field_name,
                extracted_value=None,
                confidence_score=0.0,
                source_url="error",
                extraction_method="llm_plant_details_error",
                source_type="error"
            )

    def _process_plant_response(self, field_name: str, raw_response: str) -> tuple:
        """Process and validate LLM response for plant fields with nested JSON support."""

        if not raw_response or raw_response.lower() in ["unknown", "not found", "n/a", "", "none"]:
            return self._get_default_plant_value(field_name), 0.0

        try:
            # Handle JSON fields with enhanced nested parsing
            if field_name in ["units_id", "grid_connectivity", "grid_connectivity_maps", "ppa_details"]:
                # Clean the response first
                cleaned_response = raw_response.strip()

                # Try to extract JSON from the response
                if '[' in cleaned_response or '{' in cleaned_response:
                    # Find JSON part in the response
                    start_idx = max(cleaned_response.find('['), cleaned_response.find('{'))
                    if start_idx != -1:
                        json_part = cleaned_response[start_idx:]
                        # Find the end of JSON
                        if json_part.startswith('['):
                            end_idx = json_part.rfind(']') + 1
                        else:
                            end_idx = json_part.rfind('}') + 1

                        if end_idx > 0:
                            json_str = json_part[:end_idx]
                            try:
                                parsed_value = json.loads(json_str)

                                # Enhanced validation with nested structure support
                                if field_name == "units_id" and isinstance(parsed_value, list):
                                    return parsed_value, 0.8
                                elif field_name in ["grid_connectivity", "grid_connectivity_maps"]:
                                    return self._process_grid_connectivity_json_with_pydantic(parsed_value)
                                elif field_name == "ppa_details":
                                    return self._process_ppa_details_json_with_pydantic(parsed_value)
                                else:
                                    return self._get_default_plant_value(field_name), 0.3
                            except json.JSONDecodeError as e:
                                logger.warning(f"JSON decode error for {field_name}: {e}")
                                return self._get_default_plant_value(field_name), 0.2

                # If no valid JSON found, try to extract simple list for units_id
                if field_name == "units_id":
                    # Look for unit patterns in text
                    import re
                    unit_patterns = re.findall(r'(?:Unit|unit|UNIT)\s*(\d+)', cleaned_response)
                    if unit_patterns:
                        units = [f"Unit {num}" for num in unit_patterns]
                        return units, 0.6

                return self._get_default_plant_value(field_name), 0.1

            # Handle coordinate fields with better validation
            elif field_name in ["lat", "long"]:
                # Extract numeric values from response
                import re
                # Look for decimal numbers (coordinates)
                coord_pattern = r'[-+]?\d*\.?\d+'
                matches = re.findall(coord_pattern, raw_response)

                for match in matches:
                    try:
                        coord_val = float(match)
                        # Basic coordinate validation
                        if field_name == "lat" and -90 <= coord_val <= 90:
                            return str(coord_val), 0.9
                        elif field_name == "long" and -180 <= coord_val <= 180:
                            return str(coord_val), 0.9
                    except ValueError:
                        continue

                return "", 0.0

            # Handle text fields with better cleaning
            else:
                cleaned_response = raw_response.strip().strip('"').strip("'")

                # Remove common prefixes
                prefixes_to_remove = ["address:", "location:", "plant address:", "address is:"]
                for prefix in prefixes_to_remove:
                    if cleaned_response.lower().startswith(prefix):
                        cleaned_response = cleaned_response[len(prefix):].strip()

                if len(cleaned_response) > 3:  # Minimum meaningful length
                    return cleaned_response, 0.8
                else:
                    return self._get_default_plant_value(field_name), 0.0

        except Exception as e:
            logger.error(f"Error processing response for {field_name}: {e}")
            return self._get_default_plant_value(field_name), 0.0

    def _combine_content(self, scraped_contents: List[ScrapedContent]) -> str:
        """Combine scraped content for extraction."""
        combined_text = []

        for content in scraped_contents[:5]:  # Limit to top 5 sources
            # Handle both ScrapedContent objects and strings
            if hasattr(content, 'content'):
                # It's a ScrapedContent object
                if content.content and len(content.content) > 100:
                    combined_text.append(f"Source: {content.url}\n{content.content[:2000]}\n")
            elif isinstance(content, str):
                # It's a string (fallback)
                if len(content) > 100:
                    combined_text.append(f"Content: {content[:2000]}\n")

        return "\n".join(combined_text)

    def _get_default_plant_value(self, field_name: str) -> Any:
        """Get default value for plant detail fields."""
        defaults = {
            "name": "",
            "plant_type": "",
            "plant_address": "",
            "lat": "",
            "long": "",
            "plant_id": 1,
            "units_id": [],
            "grid_connectivity_maps": [],
            "ppa_details": []
        }
        return defaults.get(field_name, "")

    def _get_empty_plant_details(self) -> Dict[str, Any]:
        """Get empty plant details structure."""
        return {
            "name": "",
            "plant_type": "",
            "plant_address": "",
            "lat": "",
            "long": "",
            "plant_id": 1,
            "units_id": [],
            "grid_connectivity_maps": [],
            "ppa_details": []
        }

    def _generate_plant_id(self, plant_name: str) -> int:
        """Generate a plant ID from plant name."""
        # For single plant extraction, always return 1
        # In future, this could be enhanced to handle multiple plants
        return 1

    def _process_complex_fields(self, extracted_data: Dict[str, Any]) -> Dict[str, Any]:
        """Process complex nested fields like grid connectivity and PPA details."""

        # Process grid connectivity with proper structure
        grid_connectivity = extracted_data.get("grid_connectivity", [])
        if grid_connectivity and isinstance(grid_connectivity, list) and len(grid_connectivity) > 0:
            # If we have valid grid connectivity data, use it
            extracted_data["grid_connectivity_maps"] = grid_connectivity
        elif isinstance(grid_connectivity, dict):
            # Convert single dict to proper array structure
            extracted_data["grid_connectivity_maps"] = [{
                "description": "Grid connectivity information",
                "details": [grid_connectivity]
            }]
        else:
            # Default empty structure
            extracted_data["grid_connectivity_maps"] = []

        # Remove the temporary grid_connectivity field
        extracted_data.pop("grid_connectivity", None)

        # Process PPA details with proper structure
        ppa_details = extracted_data.get("ppa_details", [])
        if isinstance(ppa_details, list):
            # Validate each PPA entry has required structure
            validated_ppas = []
            for ppa in ppa_details:
                if isinstance(ppa, dict):
                    # Ensure required fields exist
                    validated_ppa = {
                        "description": ppa.get("description", "Power Purchase Agreement"),
                        "capacity": ppa.get("capacity", ""),
                        "capacity_unit": ppa.get("capacity_unit", "MW"),
                        "start_date": ppa.get("start_date", ""),
                        "end_date": ppa.get("end_date", ""),
                        "tenure": ppa.get("tenure"),
                        "tenure_type": ppa.get("tenure_type", "Years"),
                        "respondents": ppa.get("respondents", [])
                    }
                    validated_ppas.append(validated_ppa)
            extracted_data["ppa_details"] = validated_ppas
        else:
            extracted_data["ppa_details"] = []

        # Ensure units_id is a proper list of integers
        units_id = extracted_data.get("units_id", [])
        if not isinstance(units_id, list):
            if isinstance(units_id, str):
                # Try to split string into list and convert to integers
                try:
                    extracted_data["units_id"] = [int(unit.strip()) for unit in units_id.split(",") if unit.strip().isdigit()]
                except ValueError:
                    extracted_data["units_id"] = []
            else:
                extracted_data["units_id"] = []
        else:
            # Ensure all items in the list are integers
            try:
                extracted_data["units_id"] = [int(unit) for unit in units_id if isinstance(unit, (int, str)) and str(unit).isdigit()]
            except (ValueError, TypeError):
                extracted_data["units_id"] = []

        # Validate and clean plant_address
        plant_address = extracted_data.get("plant_address", "")
        if isinstance(plant_address, str):
            # Clean up common address formatting issues
            plant_address = plant_address.strip()
            # Remove redundant plant name from address
            plant_name = extracted_data.get("name", "")
            if plant_name and plant_name.lower() in plant_address.lower():
                # Try to extract just the address part
                parts = plant_address.split(",")
                cleaned_parts = [part.strip() for part in parts if part.strip() and plant_name.lower() not in part.lower()]
                if cleaned_parts:
                    plant_address = ", ".join(cleaned_parts)
            extracted_data["plant_address"] = plant_address

        return extracted_data

    def _process_grid_connectivity_json(self, parsed_value: Any) -> tuple:
        """Process grid connectivity JSON with nested structure support."""
        try:
            if isinstance(parsed_value, list):
                # Validate each grid connectivity entry
                validated_grid = []
                for item in parsed_value:
                    if isinstance(item, dict):
                        # Ensure proper structure with description and nested details
                        grid_item = {
                            "description": item.get("description", "Grid connectivity information"),
                            "substation": item.get("substation", ""),
                            "voltage_level": item.get("voltage_level", ""),
                            "transmission_line": item.get("transmission_line", ""),
                            "grid_operator": item.get("grid_operator", ""),
                            "connection_type": item.get("connection_type", ""),
                            "details": item.get("details", [])
                        }

                        # If details is not a list, try to convert it
                        if not isinstance(grid_item["details"], list):
                            if isinstance(grid_item["details"], dict):
                                grid_item["details"] = [grid_item["details"]]
                            else:
                                grid_item["details"] = []

                        validated_grid.append(grid_item)
                    elif isinstance(item, str):
                        # Convert string to proper structure
                        validated_grid.append({
                            "description": item,
                            "substation": "",
                            "voltage_level": "",
                            "transmission_line": "",
                            "grid_operator": "",
                            "connection_type": "",
                            "details": []
                        })

                return validated_grid, 0.8

            elif isinstance(parsed_value, dict):
                # Single grid connectivity item
                grid_item = {
                    "description": parsed_value.get("description", "Grid connectivity information"),
                    "substation": parsed_value.get("substation", ""),
                    "voltage_level": parsed_value.get("voltage_level", ""),
                    "transmission_line": parsed_value.get("transmission_line", ""),
                    "grid_operator": parsed_value.get("grid_operator", ""),
                    "connection_type": parsed_value.get("connection_type", ""),
                    "details": parsed_value.get("details", [])
                }

                # Ensure details is a list
                if not isinstance(grid_item["details"], list):
                    if isinstance(grid_item["details"], dict):
                        grid_item["details"] = [grid_item["details"]]
                    else:
                        grid_item["details"] = []

                return [grid_item], 0.8

            else:
                return [], 0.2

        except Exception as e:
            logger.error(f"Error processing grid connectivity JSON: {e}")
            return [], 0.1

    def _process_ppa_details_json(self, parsed_value: Any) -> tuple:
        """Process PPA details JSON with nested structure support."""
        try:
            if isinstance(parsed_value, list):
                # Validate each PPA entry
                validated_ppas = []
                for item in parsed_value:
                    if isinstance(item, dict):
                        # Ensure proper PPA structure
                        ppa_item = {
                            "description": item.get("description", "Power Purchase Agreement"),
                            "capacity": item.get("capacity", ""),
                            "capacity_unit": item.get("capacity_unit", "MW"),
                            "start_date": item.get("start_date", ""),
                            "end_date": item.get("end_date", ""),
                            "tenure": item.get("tenure", ""),
                            "tenure_type": item.get("tenure_type", "Years"),
                            "respondents": item.get("respondents", [])
                        }

                        # Validate respondents structure
                        if not isinstance(ppa_item["respondents"], list):
                            ppa_item["respondents"] = []
                        else:
                            # Ensure each respondent has proper structure
                            validated_respondents = []
                            for resp in ppa_item["respondents"]:
                                if isinstance(resp, dict):
                                    validated_respondents.append({
                                        "name": resp.get("name", ""),
                                        "capacity": resp.get("capacity", ""),
                                        "percentage": resp.get("percentage", ""),
                                        "type": resp.get("type", "")
                                    })
                                elif isinstance(resp, str):
                                    validated_respondents.append({
                                        "name": resp,
                                        "capacity": "",
                                        "percentage": "",
                                        "type": ""
                                    })
                            ppa_item["respondents"] = validated_respondents

                        validated_ppas.append(ppa_item)
                    elif isinstance(item, str):
                        # Convert string to proper structure
                        validated_ppas.append({
                            "description": item,
                            "capacity": "",
                            "capacity_unit": "MW",
                            "start_date": "",
                            "end_date": "",
                            "tenure": "",
                            "tenure_type": "Years",
                            "respondents": []
                        })

                return validated_ppas, 0.8

            elif isinstance(parsed_value, dict):
                # Single PPA item
                ppa_item = {
                    "description": parsed_value.get("description", "Power Purchase Agreement"),
                    "capacity": parsed_value.get("capacity", ""),
                    "capacity_unit": parsed_value.get("capacity_unit", "MW"),
                    "start_date": parsed_value.get("start_date", ""),
                    "end_date": parsed_value.get("end_date", ""),
                    "tenure": parsed_value.get("tenure", ""),
                    "tenure_type": parsed_value.get("tenure_type", "Years"),
                    "respondents": parsed_value.get("respondents", [])
                }

                # Validate respondents structure
                if not isinstance(ppa_item["respondents"], list):
                    ppa_item["respondents"] = []

                return [ppa_item], 0.8

            else:
                return [], 0.2

        except Exception as e:
            logger.error(f"Error processing PPA details JSON: {e}")
            return [], 0.1

    def _process_grid_connectivity_json_with_pydantic(self, parsed_value: Any) -> tuple:
        """Process grid connectivity JSON using Pydantic models for validation."""
        try:
            from src.models import GridConnectivityMap, SubstationDetails, SubstationProject

            validated_maps = []

            # Handle different input formats
            if isinstance(parsed_value, list):
                for item in parsed_value:
                    if isinstance(item, dict):
                        # Parse substation details with Pydantic validation
                        details = []
                        if "details" in item and isinstance(item["details"], list):
                            for detail in item["details"]:
                                if isinstance(detail, dict):
                                    # Parse projects if present
                                    projects = []
                                    if "projects" in detail and isinstance(detail["projects"], list):
                                        for proj in detail["projects"]:
                                            if isinstance(proj, dict):
                                                project = SubstationProject(
                                                    description=proj.get("description", ""),
                                                    distance=proj.get("distance", "")
                                                )
                                                projects.append(project.dict())

                                    # Create SubstationDetails with validation
                                    substation = SubstationDetails(
                                        description=detail.get("description", ""),
                                        capacity=detail.get("capacity", ""),
                                        latitude=detail.get("latitude", ""),
                                        longitude=detail.get("longitude", ""),
                                        projects=projects,
                                        substation_name=detail.get("substation_name", ""),
                                        substation_type=detail.get("substation_type", "")
                                    )
                                    details.append(substation.dict())

                        # Create GridConnectivityMap with validation
                        grid_map = GridConnectivityMap(
                            description=item.get("description", "Grid connectivity information"),
                            details=details
                        )
                        validated_maps.append(grid_map.dict())

            elif isinstance(parsed_value, dict):
                # Single grid connectivity item
                details = []
                if "details" in parsed_value and isinstance(parsed_value["details"], list):
                    for detail in parsed_value["details"]:
                        if isinstance(detail, dict):
                            # Parse projects if present
                            projects = []
                            if "projects" in detail and isinstance(detail["projects"], list):
                                for proj in detail["projects"]:
                                    if isinstance(proj, dict):
                                        project = SubstationProject(
                                            description=proj.get("description", ""),
                                            distance=proj.get("distance", "")
                                        )
                                        projects.append(project.dict())

                            # Create SubstationDetails with validation
                            substation = SubstationDetails(
                                description=detail.get("description", ""),
                                capacity=detail.get("capacity", ""),
                                latitude=detail.get("latitude", ""),
                                longitude=detail.get("longitude", ""),
                                projects=projects,
                                substation_name=detail.get("substation_name", ""),
                                substation_type=detail.get("substation_type", "")
                            )
                            details.append(substation.dict())

                grid_map = GridConnectivityMap(
                    description=parsed_value.get("description", "Grid connectivity information"),
                    details=details
                )
                validated_maps.append(grid_map.dict())

            if validated_maps:
                logger.info(f"Successfully parsed {len(validated_maps)} grid connectivity maps with Pydantic")
                return validated_maps, 0.9
            else:
                return [], 0.2

        except Exception as e:
            logger.error(f"Pydantic validation error for grid connectivity: {e}")
            return [], 0.1

    def _process_ppa_details_json_with_pydantic(self, parsed_value: Any) -> tuple:
        """Process PPA details JSON using Pydantic models for validation."""
        try:
            from src.models import PPADetails, PPARespondent

            validated_ppas = []

            # Handle different input formats
            if isinstance(parsed_value, list):
                for item in parsed_value:
                    if isinstance(item, dict):
                        # Parse respondents with Pydantic validation
                        respondents = []
                        if "respondents" in item and isinstance(item["respondents"], list):
                            for resp in item["respondents"]:
                                if isinstance(resp, dict):
                                    respondent = PPARespondent(
                                        name=resp.get("name", ""),
                                        capacity=resp.get("capacity", ""),
                                        currency=resp.get("currency", ""),
                                        price=resp.get("price", ""),
                                        price_unit=resp.get("price_unit", "")
                                    )
                                    respondents.append(respondent.dict())
                                elif isinstance(resp, str):
                                    # Handle string respondents
                                    respondent = PPARespondent(
                                        name=resp,
                                        capacity="",
                                        currency="",
                                        price="",
                                        price_unit=""
                                    )
                                    respondents.append(respondent.dict())

                        # Create PPADetails with validation
                        ppa = PPADetails(
                            description=item.get("description", "Power Purchase Agreement"),
                            capacity=item.get("capacity", ""),
                            capacity_unit=item.get("capacity_unit", "MW"),
                            start_date=item.get("start_date", ""),
                            end_date=item.get("end_date", ""),
                            tenure=item.get("tenure"),
                            tenure_type=item.get("tenure_type", "Years"),
                            respondents=respondents
                        )
                        validated_ppas.append(ppa.dict())

            elif isinstance(parsed_value, dict):
                # Single PPA item
                respondents = []
                if "respondents" in parsed_value and isinstance(parsed_value["respondents"], list):
                    for resp in parsed_value["respondents"]:
                        if isinstance(resp, dict):
                            respondent = PPARespondent(
                                name=resp.get("name", ""),
                                capacity=resp.get("capacity", ""),
                                currency=resp.get("currency", ""),
                                price=resp.get("price", ""),
                                price_unit=resp.get("price_unit", "")
                            )
                            respondents.append(respondent.dict())
                        elif isinstance(resp, str):
                            # Handle string respondents
                            respondent = PPARespondent(
                                name=resp,
                                capacity="",
                                currency="",
                                price="",
                                price_unit=""
                            )
                            respondents.append(respondent.dict())

                ppa = PPADetails(
                    description=parsed_value.get("description", "Power Purchase Agreement"),
                    capacity=parsed_value.get("capacity", ""),
                    capacity_unit=parsed_value.get("capacity_unit", "MW"),
                    start_date=parsed_value.get("start_date", ""),
                    end_date=parsed_value.get("end_date", ""),
                    tenure=parsed_value.get("tenure"),
                    tenure_type=parsed_value.get("tenure_type", "Years"),
                    respondents=respondents
                )
                validated_ppas.append(ppa.dict())

            if validated_ppas:
                logger.info(f"Successfully parsed {len(validated_ppas)} PPA details with Pydantic")
                return validated_ppas, 0.9
            else:
                return [], 0.2

        except Exception as e:
            logger.error(f"Pydantic validation error for PPA details: {e}")
            return [], 0.1

    async def extract_single_field(
        self,
        plant_name: str,
        field_name: str,
        scraped_contents: List[ScrapedContent]
    ) -> Optional[str]:
        """
        Extract a single specific field from scraped content.

        Args:
            plant_name: Name of the power plant
            field_name: Name of the field to extract
            scraped_contents: List of scraped content to analyze

        Returns:
            Extracted field value or None
        """
        if not scraped_contents:
            return None

        try:
            # Get the prompt for this specific field
            prompts = config.plant_details_extraction_prompts
            if field_name not in prompts:
                logger.warning(f"No prompt found for field: {field_name}")
                return None

            prompt_template = prompts[field_name]

            # Combine content from all sources
            combined_content = self._combine_content(scraped_contents)
            if not combined_content:
                return None

            # Format the prompt
            formatted_prompt = prompt_template.format(
                plant_name=plant_name,
                content=combined_content[:10000]  # Limit content length
            )

            # Extract using LLM
            raw_response = await self.groq_client.extract_field(formatted_prompt)

            if not raw_response:
                return None

            # Process the response
            processed_value, confidence = self._process_plant_response(field_name, raw_response)

            # Only return if confidence is reasonable
            if confidence > 0.3:
                logger.info(f"Single field extraction - {field_name}: {processed_value} (confidence: {confidence:.2f})")
                return processed_value
            else:
                logger.info(f"Single field extraction - {field_name}: low confidence ({confidence:.2f})")
                return None

        except Exception as e:
            logger.error(f"Error extracting single field {field_name}: {e}")
            return None