# Files Recommended for Removal

## Summary
Based on comprehensive codebase analysis, the following files are either unused, redundant, or superseded by better implementations.

## 🗑️ Files to Remove

### Unused/Orphaned Source Files
- `src/openai_client.py` - Only used as fallback, user prefers Groq
- `src/pdf_processor.py` - Rarely used PDF processing functionality  
- `src/html_first_extractor.py` - Not actively used in main pipelines
- `src/validation.py` - Minimal validation logic, not essential

### Redundant Demo Files  
- `demo.py` - Basic demo, superseded by comprehensive demos
- `demo_plant_details.py` - Functionality covered by demo_smart_pipeline.py
- `demo_complete_pipeline.py` - Mock demo, redundant with working demos
- `demo_financial_year.py` - Not part of core functionality
- `demo_cached_unit_extraction.py` - Superseded by demo_unit_schema_filling.py

### Redundant Run Scripts
- `run_main_pipeline.py` - Redundant with run_groq_rag_pipeline.py
- `run_openai_pipeline.py` - User prefers Groq over OpenAI
- `run_openai_rag_pipeline.py` - User prefers <PERSON>roq over OpenAI  
- `run_pure_rag_pipeline.py` - Superseded by better approaches
- `run_missing_field_pipeline.py` - Functionality integrated into main workflows

### Outdated Pipeline Files
- `src/pipeline.py` - Basic pipeline, superseded by smart_unified_pipeline.py
- `src/unified_pipeline.py` - Superseded by smart_unified_pipeline.py
- `src/simple_pipeline.py` - Functionality covered by sequential_web_extractor.py

### Utility Files
- `fix_and_test.py` - Testing utility, not core functionality
- `unit_level.json` - Template file, superseded by unit_details.json

## ✅ Files to Keep (Core Functionality)

### Essential Source Files
- `src/models.py` - Core data models
- `src/config.py` - Configuration management
- `src/serp_client.py` - Search functionality
- `src/scraper_client.py` - Web scraping
- `src/groq_client.py` - LLM integration (user's preferred)
- `src/enhanced_extractor.py` - Multi-strategy extraction
- `src/plant_details_extractor.py` - Plant details extraction
- `src/smart_unified_pipeline.py` - Smart cache-optimized pipeline (RECOMMENDED)
- `src/sequential_web_extractor.py` - Sequential extraction workflow
- `src/unit_details_schema_filler.py` - Unit details extraction
- `src/cache_manager.py` - Cache management
- `src/source_tracker.py` - Source tracking
- `src/field_analyzer.py` - Field analysis

### Essential Demo/Run Files
- `demo_smart_pipeline.py` - Smart pipeline demo
- `demo_sequential_web_extraction.py` - Sequential extraction demo
- `demo_unit_schema_filling.py` - Unit details demo
- `demo_source_tracking.py` - Source tracking demo
- `demo_missing_fields.py` - Missing fields demo
- `run_sequential_extraction.py` - Sequential extraction runner
- `run_unit_extraction.py` - Unit extraction runner
- `run_groq_rag_pipeline.py` - Groq RAG pipeline (user's preference)

## 📊 Impact Analysis

**Files to Remove:** 15 files
**Files to Keep:** 21 core files + documentation
**Space Saved:** ~50% reduction in codebase size
**Maintenance Reduced:** Eliminates redundant and outdated code paths

## 🎯 Recommended Action

The recommended approach is to remove the identified redundant files to:
1. Simplify the codebase
2. Reduce maintenance overhead  
3. Focus on the working, optimized pipelines
4. Align with user preferences (Groq over OpenAI/Bedrock)
5. Keep only the best-performing implementations
