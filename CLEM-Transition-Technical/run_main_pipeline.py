"""
Main pipeline runner for Jhajjar Power Plant extraction with Groq.
Uses the simplified pipeline with improved prompts for clean JSON output.
"""
import asyncio
import logging
import json
import sys
import time
from datetime import datetime
from src.simple_pipeline import SimplePowerPlantPipeline


def setup_logging():
    """Configure logging for the pipeline."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('pipeline_main.log'),
            logging.StreamHandler()
        ]
    )


async def extract_jhajjar_main():
    """
    Main extraction function for Jhajjar Power Plant.
    Uses simplified pipeline with AWS Bedrock and improved prompts.
    """
    plant_name = "Jhajjar Power Plant"
    start_time = time.time()

    try:
        print("🚀 MAIN JHAJJAR POWER PLANT EXTRACTION")
        print("=" * 60)
        print(f"🔍 Target Plant: {plant_name}")
        print(f"🧠 Method: Simplified Pipeline with Groq")
        print(f"📊 Strategy: Clean prompts → Top 5 links → Cache + Targeted searches")
        print("=" * 60)

        # Initialize the simplified pipeline
        print("\n⚙️  Initializing simplified pipeline...")
        pipeline = SimplePowerPlantPipeline()
        print("✅ Pipeline initialized successfully")

        # Extract plant data
        print(f"\n🔍 Starting main extraction for: {plant_name}")
        org_details, plant_details, extraction_info = await pipeline.extract_plant_data(plant_name)

        total_duration = time.time() - start_time

        # Save results with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        org_file = f"jhajjar_org_main_{timestamp}.json"
        plant_file = f"jhajjar_plant_main_{timestamp}.json"
        info_file = f"jhajjar_extraction_info_{timestamp}.json"

        await pipeline.save_results(
            org_details, plant_details, extraction_info,
            org_file, plant_file, info_file
        )

        print(f"\n⏱️  Total extraction time: {total_duration:.1f} seconds")

        # Display extraction metrics
        print(f"\n📊 EXTRACTION METRICS")
        print("-" * 40)
        print(f"🔍 Initial search time: {extraction_info.get('search_time', 0):.1f}s")
        print(f"📄 Total pages scraped: {extraction_info.get('pages_scraped', 0)}")
        print(f"💾 Cache hit fields: {len(extraction_info.get('cache_hit_fields', []))}")
        print(f"🎯 Missing field searches: {extraction_info.get('missing_field_searches', 0)}")

        cache_fields = extraction_info.get('cache_hit_fields', [])
        if cache_fields:
            print(f"✅ Fields from cache: {', '.join(cache_fields)}")

        targeted_searches = extraction_info.get('targeted_search_results', [])
        if targeted_searches:
            print(f"🔍 Targeted searches: {', '.join(targeted_searches)}")

        # Display results summary
        print(f"\n📋 EXTRACTION RESULTS SUMMARY")
        print("=" * 50)

        if org_details:
            # Handle both dictionary and model object
            if hasattr(org_details, 'model_dump'):
                org_data = org_details.model_dump()
            else:
                org_data = org_details

            filled_org_fields = sum(1 for v in org_data.values() if v not in [None, "", []])
            print(f"📊 Organizational Details: {filled_org_fields}/{len(org_data)} fields extracted")

            # Show key organizational info
            key_org_fields = {
                'organization_name': '🏢 Organization',
                'country_name': '🌍 Country',
                'province': '📍 Province',
                'plant_types': '⚡ Plant Types',
                'cfpp_type': '🏛️ Type',
                'plants_count': '🏭 Plants Count',
                'financial_year': '📅 Financial Year',
                'currency_in': '💰 Currency'
            }

            for field, label in key_org_fields.items():
                value = org_data.get(field)
                if value and value not in [None, "", []]:
                    if isinstance(value, list):
                        print(f"   {label}: {', '.join(map(str, value))}")
                    else:
                        print(f"   {label}: {value}")

        if plant_details:
            # Handle both dictionary and model object
            if hasattr(plant_details, 'model_dump'):
                plant_data = plant_details.model_dump()
            else:
                plant_data = plant_details

            filled_plant_fields = sum(1 for v in plant_data.values() if v not in [None, "", []])
            print(f"\n🔧 Plant Technical Details: {filled_plant_fields}/{len(plant_data)} fields extracted")

            # Show key technical info
            key_plant_fields = {
                'name': '📛 Plant Name',
                'plant_type': '⚙️ Plant Type',
                'plant_address': '📍 Address',
                'lat': '🌐 Latitude',
                'long': '🌐 Longitude',
                'units_id': '🔢 Units'
            }

            for field, label in key_plant_fields.items():
                value = plant_data.get(field)
                if value and value not in [None, "", []]:
                    if isinstance(value, list):
                        print(f"   {label}: {', '.join(map(str, value))}")
                    else:
                        # Truncate long addresses for display
                        if field == 'plant_address' and len(str(value)) > 100:
                            print(f"   {label}: {str(value)[:100]}...")
                        else:
                            print(f"   {label}: {value}")

            # Show complex fields summary
            if plant_data.get('grid_connectivity_maps'):
                print(f"   🔌 Grid Connectivity: {len(plant_data['grid_connectivity_maps'])} connections")

            if plant_data.get('ppa_details'):
                print(f"   📄 PPA Details: {len(plant_data['ppa_details'])} agreements")

        print(f"\n💾 Results saved to:")
        print(f"   📊 Organizational: {org_file}")
        print(f"   🔧 Plant Technical: {plant_file}")
        print(f"   📈 Extraction Info: {info_file}")

        # Calculate and display efficiency
        if extraction_info:
            cache_hits = len(extraction_info.get('cache_hit_fields', []))
            targeted_searches = extraction_info.get('missing_field_searches', 0)
            total_possible_fields = 19  # 9 org + 10 plant fields

            if total_possible_fields > 0:
                efficiency = (cache_hits / total_possible_fields) * 100
                print(f"\n🎯 EFFICIENCY ANALYSIS")
                print("-" * 30)
                print(f"💡 Cache efficiency: {efficiency:.1f}%")
                print(f"⚡ Fields from cache: {cache_hits}/{total_possible_fields}")
                print(f"🎯 Targeted searches: {targeted_searches}")

                if efficiency > 70:
                    print(f"🏆 Excellent cache utilization!")
                elif efficiency > 40:
                    print(f"👍 Good cache utilization")
                else:
                    print(f"📝 Room for improvement in cache efficiency")

        # Display clean JSON results
        print(f"\n📄 CLEAN JSON RESULTS")
        print("=" * 50)

        if org_details:
            print(f"\n📊 ORGANIZATIONAL DETAILS JSON:")
            print("-" * 40)
            if hasattr(org_details, 'model_dump'):
                print(json.dumps(org_details.model_dump(), indent=2, ensure_ascii=False))
            else:
                print(json.dumps(org_details, indent=2, ensure_ascii=False))

        if plant_details:
            print(f"\n🔧 PLANT TECHNICAL DETAILS JSON:")
            print("-" * 40)
            if hasattr(plant_details, 'model_dump'):
                print(json.dumps(plant_details.model_dump(), indent=2, ensure_ascii=False))
            else:
                print(json.dumps(plant_details, indent=2, ensure_ascii=False))

        print(f"\n🎉 Main Jhajjar Power Plant extraction completed successfully!")
        if org_details and plant_details:
            # Handle both dictionary and model object for counting
            if hasattr(org_details, 'model_dump'):
                org_count = sum(1 for v in org_details.model_dump().values() if v not in [None, "", []])
            else:
                org_count = sum(1 for v in org_details.values() if v not in [None, "", []])

            if hasattr(plant_details, 'model_dump'):
                plant_count = sum(1 for v in plant_details.model_dump().values() if v not in [None, "", []])
            else:
                plant_count = sum(1 for v in plant_details.values() if v not in [None, "", []])

            print(f"📊 Total fields extracted: {org_count + plant_count}")
        print(f"⏱️  Total time: {total_duration:.1f} seconds")

        return org_details, plant_details, extraction_info

    except Exception as e:
        print(f"\n❌ Extraction failed for {plant_name}: {e}")
        logging.error(f"Extraction failed for {plant_name}: {e}", exc_info=True)
        raise


async def main():
    """Main execution function."""
    setup_logging()

    print("🚀 MAIN PIPELINE - JHAJJAR POWER PLANT EXTRACTION")
    print("Using simplified pipeline with Groq and improved prompts")
    print("Clean JSON output with targeted field extraction!")
    print()

    try:
        # Run the extraction
        org_details, plant_details, extraction_info = await extract_jhajjar_main()

        print(f"\n✅ MAIN PIPELINE COMPLETED SUCCESSFULLY!")
        print("Check the generated JSON files for complete results.")

    except Exception as e:
        print(f"\n❌ MAIN PIPELINE FAILED: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
