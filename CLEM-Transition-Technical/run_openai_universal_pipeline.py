#!/usr/bin/env python3
"""
🚀 UNIVERSAL OPENAI-POWERED UNIT EXTRACTION PIPELINE
Complete 3-level extraction: Organizational → Plant Technical → Unit Details
Using OpenAI GPT-4o with universal tiered extraction strategy
Works globally for any power plant with intelligent fallbacks
"""

import asyncio
import json
import time
import logging
from datetime import datetime
from pathlib import Path
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import our modules
from src.simple_pipeline import SimplePowerPlantPipeline
from src.openai_extraction_client import OpenAIExtractionClient
from src.serp_client import SerpAPIClient, PowerPlantSearchOrchestrator
from src.scraper_client import ScraperAPIClient
from run_groq_rag_pipeline import GroqRAGPipeline

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class UniversalOpenAIPipeline(GroqRAGPipeline):
    """
    Universal OpenAI-powered pipeline for global power plant unit extraction.
    Extends GroqRAGPipeline but uses OpenAI GPT-4o instead of Groq.
    """

    def __init__(self, openai_api_key: str, openai_model: str = "gpt-4o"):
        """Initialize the universal OpenAI pipeline."""
        # Initialize parent class (no arguments needed)
        super().__init__()

        # Replace Groq client with OpenAI client
        self.openai_client = OpenAIExtractionClient(openai_api_key, openai_model)
        self.openai_api_key = openai_api_key
        self.openai_model = openai_model

        # Ensure we have access to scraper_api_key from parent class
        # The parent class gets it from config, so we should have it

        logger.info(f"Universal OpenAI pipeline initialized with model: {openai_model}")

    async def extract_unit_fields_tiered_openai(self, plant_name, unit_id, unit_template, org_details, plant_details,
                                               scraped_contents, unit_extraction_info):
        """Extract unit fields using OpenAI with tiered approach based on success rates."""

        # Initialize unit data with basic fields
        unit_data = {
            "unit_number": str(unit_id),
            "plant_id": plant_details.get("plant_id", 1) if isinstance(plant_details, dict) else 1
        }

        # Copy PPA details from plant level (already extracted)
        if isinstance(plant_details, dict) and plant_details.get("ppa_details"):
            unit_data["ppa_details"] = plant_details["ppa_details"]
            print(f"      📋 ppa_details: Copied from plant level")
        elif hasattr(plant_details, 'ppa_details') and plant_details.ppa_details:
            unit_data["ppa_details"] = plant_details.ppa_details
            print(f"      📋 ppa_details: Copied from plant level")

        # Get extraction tiers and fallback strategies
        field_tiers = self._get_field_extraction_tiers()
        fallback_strategies = self._get_fallback_strategies()

        # Extract country for country-specific strategies
        country = getattr(org_details, 'country_name', 'Unknown') if org_details else 'Unknown'

        # Combine all scraped content
        combined_content = "\n\n".join([content.content for content in scraped_contents[:3]])

        print(f"🎯 Starting OpenAI tiered field extraction for Unit {unit_id}")

        # Phase 1: High Success Fields (Priority extraction) - Use batch extraction
        print(f"📊 Phase 1: Extracting high-success fields with OpenAI...")
        await self._extract_field_tier_openai(
            field_tiers["high_success"], unit_template, unit_data,
            plant_name, unit_id, country, combined_content,
            unit_extraction_info, confidence_threshold=0.7, tier_name="high_success"
        )

        # Phase 2: Medium Success Fields
        print(f"📊 Phase 2: Extracting medium-success fields with OpenAI...")
        await self._extract_field_tier_openai(
            field_tiers["medium_success"], unit_template, unit_data,
            plant_name, unit_id, country, combined_content,
            unit_extraction_info, confidence_threshold=0.6, tier_name="medium_success"
        )

        # Phase 3: Low Success Fields (Research-intensive)
        print(f"📊 Phase 3: Extracting low-success fields with OpenAI...")
        await self._extract_field_tier_openai(
            field_tiers["low_success"], unit_template, unit_data,
            plant_name, unit_id, country, combined_content,
            unit_extraction_info, confidence_threshold=0.5, tier_name="low_success"
        )

        # Phase 4: Very Low Success Fields (Best effort)
        print(f"📊 Phase 4: Extracting very-low-success fields with OpenAI...")
        await self._extract_field_tier_openai(
            field_tiers["very_low_success"], unit_template, unit_data,
            plant_name, unit_id, country, combined_content,
            unit_extraction_info, confidence_threshold=0.4, tier_name="very_low_success"
        )

        # Phase 5: Apply fallback strategies for missing fields
        print(f"🔧 Phase 5: Applying fallback strategies for missing fields...")
        self._apply_fallback_strategies(
            unit_data, unit_template, plant_details, org_details,
            fallback_strategies, unit_extraction_info
        )

        return unit_data

    async def _extract_field_tier_openai(self, field_list, unit_template, unit_data, plant_name, unit_id,
                                        country, combined_content, unit_extraction_info,
                                        confidence_threshold=0.6, tier_name="unknown"):
        """Extract a specific tier of fields using OpenAI with batch processing."""

        # Filter fields that need extraction
        fields_to_extract = []
        for field_name in field_list:
            if field_name not in unit_template:
                continue
            if field_name in ["unit_number", "plant_id", "ppa_details"]:
                continue  # Already handled
            if field_name in unit_data and unit_data[field_name]:
                continue  # Already extracted
            fields_to_extract.append(field_name)

        if not fields_to_extract:
            print(f"      ✅ All {tier_name} fields already extracted")
            return

        print(f"      🔍 Extracting {len(fields_to_extract)} {tier_name} fields: {fields_to_extract[:5]}{'...' if len(fields_to_extract) > 5 else ''}")

        # Use batch extraction for efficiency (max 5 fields per batch to avoid token limits)
        batch_size = 5
        for i in range(0, len(fields_to_extract), batch_size):
            batch_fields = fields_to_extract[i:i+batch_size]

            try:
                # Create context for this batch
                context = f"""
                Plant: {plant_name}
                Unit: Unit {unit_id}
                Country: {country}
                Tier: {tier_name}

                Extract unit-specific technical information for Unit {unit_id} of {plant_name}.
                Focus on precise technical specifications and performance data.
                """

                # Batch extract fields
                batch_results = await self.openai_client.extract_multiple_fields(
                    batch_fields, combined_content, context
                )

                # Process results
                for field_name, result in batch_results.items():
                    if result and result.confidence_score >= confidence_threshold:
                        # Post-process the extracted value
                        processed_value = self._post_process_field_value(field_name, result.extracted_value, plant_name, unit_id)
                        unit_data[field_name] = processed_value
                        unit_extraction_info["fields_extracted"] += 1

                        # Track success by tier
                        tier_key = f"{tier_name}_extracted"
                        if tier_key in unit_extraction_info["field_success_rates"]:
                            unit_extraction_info["field_success_rates"][tier_key] += 1

                        print(f"      ✅ {field_name}: {str(processed_value)[:50]}{'...' if len(str(processed_value)) > 50 else ''} (conf: {result.confidence_score:.2f})")
                    else:
                        print(f"      ⚠️  {field_name}: Low confidence ({result.confidence_score if result else 0:.2f})")

                # Rate limiting between batches
                if i + batch_size < len(fields_to_extract):
                    await asyncio.sleep(1)  # 1 second between batches

            except Exception as e:
                print(f"      ❌ Batch extraction failed for {batch_fields}: {e}")
                continue

    async def _extract_single_unit_comprehensive_openai(self, plant_name, unit_id, unit_template, org_details, plant_details):
        """Extract single unit using comprehensive OpenAI approach."""

        unit_extraction_info = {
            "unit_id": unit_id,
            "web_searches": 0,
            "pages_scraped": 0,
            "fields_extracted": 0,
            "extraction_method": "openai_universal_tiered",
            "field_success_rates": {
                "high_success_extracted": 0,
                "medium_success_extracted": 0,
                "low_success_extracted": 0,
                "very_low_success_extracted": 0,
                "fallback_applied": 0,
                "total_fields": len(unit_template)
            },
            "extraction_sources": [],
            "fallback_strategies_used": []
        }

        try:
            print(f"🔍 Step 1: Comprehensive search for Unit {unit_id}")

            # Generate universal search queries
            unit_search_queries = self._generate_universal_search_queries(plant_name, unit_id, org_details, plant_details)

            # Perform comprehensive search with enhanced error handling
            async with SerpAPIClient(self.serp_api_key) as serp_client:
                search_orchestrator = PowerPlantSearchOrchestrator(serp_client)

                all_unit_search_results = []
                for i, query in enumerate(unit_search_queries):
                    try:
                        # Enhanced rate limiting
                        if i > 0:
                            delay = min(1 + (i // 5), 5)  # Progressive delay, max 5 seconds
                            await asyncio.sleep(delay)

                        search_results = await search_orchestrator.search_specific_field(query, max_results=2)
                        all_unit_search_results.extend(search_results)
                        unit_extraction_info["web_searches"] += 1

                        print(f"      🔍 Query {i+1}/{len(unit_search_queries)}: Found {len(search_results)} results")

                        if len(all_unit_search_results) >= 15:  # Limit total results
                            print(f"      ⚠️  Reached result limit ({len(all_unit_search_results)} results), stopping search")
                            break

                    except Exception as e:
                        print(f"      ❌ Search query {i+1} failed: {e}")
                        continue

            print(f"📊 Found {len(all_unit_search_results)} search results for Unit {unit_id}")

            # Step 2: Scrape content
            if all_unit_search_results:
                print(f"📄 Step 2: Scraping content for Unit {unit_id}")

                # Use config for scraper API key like parent class
                import config
                async with ScraperAPIClient(config.pipeline.scraper_api_key) as scraper_client:
                    scraped_contents = []
                    urls_to_scrape = [result.url for result in all_unit_search_results[:8]]  # Limit to top 8 URLs

                    for i, url in enumerate(urls_to_scrape):
                        try:
                            print(f"        📄 Scraping {i+1}/{len(urls_to_scrape)}: {url}")
                            content = await scraper_client.scrape_url(url)
                            if content:
                                scraped_contents.append(content)
                            # Rate limiting
                            await asyncio.sleep(1)
                        except Exception as e:
                            print(f"        ❌ Failed to scrape {url}: {e}")
                            continue

                    unit_extraction_info["pages_scraped"] = len(scraped_contents)

                print(f"📋 Scraped {len(scraped_contents)} pages for Unit {unit_id}")

                # Step 3: Extract fields using OpenAI with tiered approach
                if scraped_contents:
                    print(f"🧠 Step 3: Extracting fields for Unit {unit_id} using OpenAI GPT-4o")

                    # Use our new OpenAI tiered extraction
                    unit_data = await self.extract_unit_fields_tiered_openai(
                        plant_name, unit_id, unit_template, org_details, plant_details,
                        scraped_contents, unit_extraction_info
                    )

                else:
                    print(f"❌ No content scraped for Unit {unit_id}")
                    unit_data = {"unit_number": str(unit_id), "plant_id": 1}
            else:
                print(f"❌ No search results found for Unit {unit_id}")
                unit_data = {"unit_number": str(unit_id), "plant_id": 1}

            print(f"🎉 Unit {unit_id} OpenAI extraction completed: {unit_extraction_info['fields_extracted']} fields extracted")
            return unit_data, unit_extraction_info

        except Exception as e:
            print(f"❌ Unit {unit_id} extraction failed: {e}")
            logging.error(f"Unit extraction failed for {plant_name} Unit {unit_id}: {e}", exc_info=True)
            return {"unit_number": str(unit_id), "plant_id": 1}, unit_extraction_info

async def main():
    """Main execution function for universal OpenAI pipeline."""

    print("🚀 UNIVERSAL OPENAI-POWERED UNIT EXTRACTION PIPELINE")
    print("=" * 70)
    print("🔍 Target Plant: Jhajjar Power Plant")
    print("🧠 Method: Complete 3-Level Pipeline with OpenAI GPT-4o")
    print("📊 Strategy: Org → Plant → Units | Universal Tiered Extraction")
    print("⚡ Model: GPT-4o (Best quality, high rate limits)")
    print("=" * 70)

    start_time = time.time()

    # Get API keys from environment
    serp_api_key = os.getenv("SERP_API_KEY")
    scraper_api_key = os.getenv("SCRAPER_API_KEY")
    openai_api_key = os.getenv("OPENAI_API_KEY")
    openai_model = os.getenv("OPENAI_MODEL", "gpt-4o")

    if not all([serp_api_key, scraper_api_key, openai_api_key]):
        print("❌ Missing required API keys in .env file")
        return

    # Initialize pipeline
    print(f"⚙️  Initializing Universal OpenAI pipeline with {openai_model}...")
    pipeline = UniversalOpenAIPipeline(openai_api_key, openai_model)
    print(f"✅ Universal OpenAI pipeline initialized successfully")

    plant_name = "Jhajjar Power Plant"

    # Extract organizational details (Level 1)
    print(f"\n🏢 Starting organizational extraction for: {plant_name}")
    simple_pipeline = SimplePowerPlantPipeline()
    org_details, plant_details, extraction_info = await simple_pipeline.extract_plant_data(plant_name)

    # Extract unit details (Level 3) using OpenAI
    print(f"\n⚡ Starting OpenAI unit-level extraction for: {plant_name}")

    # Override the unit extraction method to use OpenAI
    original_method = pipeline._extract_single_unit_comprehensive
    pipeline._extract_single_unit_comprehensive = pipeline._extract_single_unit_comprehensive_openai

    unit_details, unit_extraction_info = await pipeline.extract_unit_data(plant_name, org_details, plant_details)

    total_duration = time.time() - start_time

    # Save results with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    org_file = f"jhajjar_org_openai_{timestamp}.json"
    plant_file = f"jhajjar_plant_openai_{timestamp}.json"
    unit_file = f"jhajjar_unit_openai_{timestamp}.json"
    info_file = f"jhajjar_extraction_info_openai_{timestamp}.json"

    # Save all results
    with open(org_file, 'w', encoding='utf-8') as f:
        json.dump(org_details.__dict__ if hasattr(org_details, '__dict__') else org_details, f, indent=2, ensure_ascii=False)

    with open(plant_file, 'w', encoding='utf-8') as f:
        # Convert PlantDetails object to dict if needed
        plant_data = plant_details.__dict__ if hasattr(plant_details, '__dict__') else plant_details
        json.dump(plant_data, f, indent=2, ensure_ascii=False)

    with open(unit_file, 'w', encoding='utf-8') as f:
        json.dump(unit_details, f, indent=2, ensure_ascii=False)

    # Compilation info
    compilation_info = {
        "pipeline_type": "universal_openai_tiered",
        "model_used": openai_model,
        "total_duration_seconds": total_duration,
        "extraction_timestamp": datetime.now().isoformat(),
        "plant_name": plant_name,
        "levels_completed": ["organizational", "plant_technical", "unit_details"],
        "unit_extraction_info": unit_extraction_info,
        "openai_usage": pipeline.openai_client.get_usage_stats(),
        "files_generated": [org_file, plant_file, unit_file, info_file]
    }

    with open(info_file, 'w', encoding='utf-8') as f:
        json.dump(compilation_info, f, indent=2, ensure_ascii=False)

    # Print summary
    print(f"\n🎉 UNIVERSAL OPENAI EXTRACTION COMPLETED!")
    print(f"⏱️  Total time: {total_duration:.1f} seconds")
    print(f"🧠 Model used: {openai_model}")
    print(f"📊 OpenAI usage: {pipeline.openai_client.get_usage_stats()}")
    print(f"💾 Results saved:")
    print(f"   📋 Organizational: {org_file}")
    print(f"   🏭 Plant Technical: {plant_file}")
    print(f"   ⚡ Unit Details: {unit_file}")
    print(f"   📊 Extraction Info: {info_file}")

    # Close OpenAI client
    await pipeline.openai_client.close()

if __name__ == "__main__":
    asyncio.run(main())
