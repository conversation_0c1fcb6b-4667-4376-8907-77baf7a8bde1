# 🚀 Final Files to Run - Quick Reference

## 🎯 Primary Production Pipeline (RECOMMENDED)

### **Final Pipeline** - Universal Hierarchical Power Plant Extraction
**Location:** `Final Pipeline/`

#### Main Entry Points:
1. **`main.py`** - Primary production script
   ```bash
   cd "Final Pipeline"
   python main.py "Plant Name"
   ```
   - Universal power plant extraction
   - Hierarchical data extraction (Org → Plant → Unit)
   - AI-powered with OpenAI GPT-4o-mini + vision
   - Complete source attribution and tracking

2. **`demo.py`** - Pipeline demonstration and overview
   ```bash
   cd "Final Pipeline"
   python demo.py
   ```
   - Shows pipeline capabilities
   - Schema analysis
   - Architecture overview
   - Usage examples

#### Key Features:
- ✅ Production-ready, clean implementation
- ✅ Schema-driven extraction (no hardcoding)
- ✅ Universal compatibility (any power plant globally)
- ✅ Vision processing for scanned PDFs
- ✅ Smart caching and hierarchical data flow
- ✅ Complete modular architecture

---

## 🔧 Legacy Working Scripts (Development/Testing)

### **CLEM-Transition-Technical** - Development and Testing
**Location:** `CLEM-Transition-Technical/`

#### Essential Run Scripts:
1. **`run_groq_rag_pipeline.py`** - Groq-based RAG pipeline
   ```bash
   cd CLEM-Transition-Technical
   python run_groq_rag_pipeline.py
   ```
   - User's preferred LLM (Groq over OpenAI)
   - RAG-enhanced extraction
   - Organizational and plant details

2. **`run_sequential_extraction.py`** - Sequential web extraction
   ```bash
   cd CLEM-Transition-Technical
   python run_sequential_extraction.py
   ```
   - Step-by-step web extraction
   - Sequential processing workflow
   - Detailed extraction logging

3. **`run_unit_extraction.py`** - Unit-level data extraction
   ```bash
   cd CLEM-Transition-Technical
   python run_unit_extraction.py
   ```
   - Focused on unit-level technical details
   - Schema-compliant unit extraction
   - Detailed technical specifications

---

## 📊 Quick Comparison

| Feature | Final Pipeline | CLEM-Transition-Technical |
|---------|---------------|---------------------------|
| **Status** | Production Ready | Development/Testing |
| **Architecture** | Clean, Modular | Legacy, Experimental |
| **AI Model** | OpenAI GPT-4o-mini | Groq Llama 3.3 70b |
| **Vision Support** | ✅ Full Vision Processing | ❌ Limited |
| **Hierarchical** | ✅ Org → Plant → Unit | ✅ Partial |
| **Universal** | ✅ Any Plant Globally | ✅ Yes |
| **Caching** | ✅ Smart Caching | ✅ Basic Caching |
| **Documentation** | ✅ Complete | ✅ Extensive |

---

## 🎯 Recommendations

### **For Production Use:**
```bash
cd "Final Pipeline"
pip install -r requirements.txt
python main.py "Jhajjar Power Plant"
```

### **For Development/Testing:**
```bash
cd CLEM-Transition-Technical
pip install -r requirements.txt
python run_groq_rag_pipeline.py
```

### **For Demonstration:**
```bash
cd "Final Pipeline"
python demo.py
```

---

## 📁 Essential File Structure

### **Final Pipeline (Production)**
```
Final Pipeline/
├── main.py                 # 🚀 PRIMARY ENTRY POINT
├── demo.py                 # 📋 DEMONSTRATION SCRIPT
├── requirements.txt        # 📦 Dependencies
├── core/                   # Configuration and models
├── clients/                # API clients (OpenAI, Scraper)
├── processors/             # Data processing and validation
├── extractors/             # Level-specific extractors
├── orchestrators/          # Pipeline coordination
├── utils/                  # Utilities and caching
└── output/                 # Generated results
```

### **CLEM-Transition-Technical (Development)**
```
CLEM-Transition-Technical/
├── run_groq_rag_pipeline.py      # 🔧 GROQ RAG PIPELINE
├── run_sequential_extraction.py  # 🔄 SEQUENTIAL EXTRACTION
├── run_unit_extraction.py        # ⚡ UNIT EXTRACTION
├── requirements.txt               # 📦 Dependencies
├── src/                           # Source modules
│   ├── groq_client.py            # Groq LLM integration
│   ├── smart_unified_pipeline.py # Smart pipeline
│   └── [other essential modules]
└── JSON outputs/                  # Historical results
```

---

## ✨ Next Steps

1. **Start with Final Pipeline** for new extractions
2. **Use CLEM-Transition-Technical** for Groq-specific needs
3. **Test both approaches** to compare results
4. **Consider migrating** fully to Final Pipeline for production

---

## 🆘 Quick Help

### **Final Pipeline Issues:**
- Check `pipeline.log` for detailed logs
- Verify API keys in `.env` file
- Review output files in `output/` directory

### **CLEM-Transition-Technical Issues:**
- Check individual log files
- Review `JSON outputs/` for historical results
- Verify Groq API configuration

### **General Issues:**
- Ensure all dependencies are installed
- Check network connectivity
- Verify API key permissions
